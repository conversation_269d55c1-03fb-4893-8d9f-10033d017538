plugins {
    id("com.android.application")
    // START: FlutterFire Configuration
    id("com.google.gms.google-services")
    // END: FlutterFire Configuration
    id("kotlin-android")
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id("dev.flutter.flutter-gradle-plugin")
}

android {

    // ----- BEGIN flavorDimensions (autogenerated by flutter_flavorizr) -----
    flavorDimensions += "flavor-type"

    productFlavors {
        create("production") {
            dimension = "flavor-type"
            applicationId = "id.co.panindaiichilife.quantumx360"
            resValue(type = "string", name = "app_name", value = "QuantumX-360")
        }
        create("dev") {
            dimension = "flavor-type"
            applicationId = "id.co.panindaiichilife.quantumx360"
            resValue(type = "string", name = "app_name", value = "[dev]QuantumX-360")
        }
    }

    // ----- <PERSON>ND flavorDimensions (autogenerated by flutter_flavorizr) -----

   namespace = "id.co.panindaiichilife.quantumx360"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = flutter.ndkVersion

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_11.toString()
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "id.co.panindaiichilife.quantumx360"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = 28
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig = signingConfigs.getByName("debug")
        }
    }
}

flutter {
    source = "../.."
}

dependencies {
    // Add UCrop dependency for image_cropper
    implementation("com.github.yalantis:ucrop:2.2.8")
}
