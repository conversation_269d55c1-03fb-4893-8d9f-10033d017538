import 'dart:developer';
import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/models/recruitment_form_model.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/logger_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

class FormFirestoreService {
  final FirebaseFirestore db = FirebaseFirestore.instance;

  // Menyimpan form ke Firestore
  Future<bool> saveRecruitmentForm(
    RecruitmentFormModel formData,
    String formId,
  ) async {
    try {
      // Dapatkan fireStoreId dari SharedPreferences
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      String fireStoreId = prefs.getString(kStorageUserFirestoreId) ?? '';

      if (fireStoreId.isEmpty) {
        _logMessage('No Firestore ID available for storing form data');
        return false;
      }

      // Persiapkan data untuk disimpan
      Map<String, dynamic> dataToStore = formData.toJson();
      // Tambahkan timestamp terakhir diperbarui
      dataToStore['lastUpdated'] = DateTime.now().millisecondsSinceEpoch;

      // Cek koneksi internet
      bool isOnline = await _checkInternetConnection();

      // Simpan ke Firestore dengan SetOptions(merge: true) untuk mempertahankan field yang sudah ada
      await db
          .collection(kFireCollectionAgent)
          .doc(fireStoreId)
          .collection('forms')
          .doc(formId)
          .set(dataToStore, SetOptions(merge: true));

      _logMessage(
        'Successfully saved form data with ID: $formId (Online: $isOnline)',
      );

      // Verifikasi data tersimpan dengan membacanya kembali dari cache
      try {
        final docSnapshot = await db
            .collection(kFireCollectionAgent)
            .doc(fireStoreId)
            .collection('forms')
            .doc(formId)
            .get(const GetOptions(source: Source.cache));

        if (docSnapshot.exists) {
          _logMessage('Verified form data is in cache for ID: $formId');
        } else {
          _logMessage(
            'Warning: Form data not found in cache immediately after storing for ID: $formId',
          );
        }
      } catch (verifyError) {
        _logMessage('Error verifying cached form data: $verifyError');
      }

      return true;
    } catch (e) {
      _logMessage('Error saving form data: $e');
      return false;
    }
  }

  // Mengambil form dari Firestore
  Future<RecruitmentFormModel?> getRecruitmentForm(String formId) async {
    try {
      // Dapatkan fireStoreId dari SharedPreferences
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      String fireStoreId = prefs.getString(kStorageUserFirestoreId) ?? '';

      if (fireStoreId.isEmpty) {
        _logMessage('No Firestore ID available for retrieving form data');
        return null;
      }

      // Selalu coba cache terlebih dahulu
      try {
        _logMessage('Explicitly trying cache first for form ID: $formId');
        DocumentSnapshot doc = await db
            .collection(kFireCollectionAgent)
            .doc(fireStoreId)
            .collection('forms')
            .doc(formId)
            .get(const GetOptions(source: Source.cache));

        if (doc.exists) {
          Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
          _logMessage(
            'Successfully retrieved cached form data for ID: $formId',
          );
          return RecruitmentFormModel.fromJson(data);
        } else {
          _logMessage('Form document does not exist in cache for ID: $formId');
        }
      } catch (cacheError) {
        _logMessage('Error retrieving form from cache: $cacheError');
      }

      // Jika cache gagal dan kita online, coba dari server
      final hasConnection = await _checkInternetConnection();
      if (hasConnection) {
        try {
          _logMessage('Trying server for form ID: $formId');
          DocumentSnapshot doc = await db
              .collection(kFireCollectionAgent)
              .doc(fireStoreId)
              .collection('forms')
              .doc(formId)
              .get(const GetOptions(source: Source.server));

          if (doc.exists) {
            Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
            _logMessage('Retrieved form data from server for ID: $formId');
            return RecruitmentFormModel.fromJson(data);
          } else {
            _logMessage(
              'Form document does not exist on server for ID: $formId',
            );
          }
        } catch (serverError) {
          _logMessage('Error retrieving form from server: $serverError');
        }
      }

      // Sebagai upaya terakhir, coba dengan opsi default
      try {
        DocumentSnapshot doc =
            await db
                .collection(kFireCollectionAgent)
                .doc(fireStoreId)
                .collection('forms')
                .doc(formId)
                .get();

        if (doc.exists) {
          Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
          _logMessage(
            'Successfully retrieved form data with default options for ID: $formId (source: ${doc.metadata.isFromCache ? "cache" : "server"})',
          );
          return RecruitmentFormModel.fromJson(data);
        }
      } catch (e) {
        _logMessage('Error retrieving form data with default options: $e');
      }

      _logMessage('No form data found for ID: $formId in cache or server');
      return null;
    } catch (e) {
      _logMessage('Error in getRecruitmentForm: $e');
      return null;
    }
  }

  // Mengambil semua form dari Firestore
  Future<List<RecruitmentFormModel>> getAllRecruitmentForms() async {
    try {
      // Dapatkan fireStoreId dari SharedPreferences
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      String fireStoreId = prefs.getString(kStorageUserFirestoreId) ?? '';

      if (fireStoreId.isEmpty) {
        _logMessage('No Firestore ID available for retrieving all forms');
        return [];
      }

      // Selalu coba cache terlebih dahulu
      try {
        _logMessage('Trying cache first for all forms');
        QuerySnapshot querySnapshot = await db
            .collection(kFireCollectionAgent)
            .doc(fireStoreId)
            .collection('forms')
            .get(const GetOptions(source: Source.cache));

        if (querySnapshot.docs.isNotEmpty) {
          List<RecruitmentFormModel> forms =
              querySnapshot.docs.map((doc) {
                Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
                // Tambahkan ID dokumen ke model
                data['id'] = doc.id;
                return RecruitmentFormModel.fromJson(data);
              }).toList();

          _logMessage(
            'Successfully retrieved ${forms.length} forms from cache',
          );
          return forms;
        } else {
          _logMessage('No forms found in cache');
        }
      } catch (cacheError) {
        _logMessage('Error retrieving forms from cache: $cacheError');
      }

      // Jika cache gagal dan kita online, coba dari server
      final hasConnection = await _checkInternetConnection();
      if (hasConnection) {
        try {
          _logMessage('Trying server for all forms');
          QuerySnapshot querySnapshot = await db
              .collection(kFireCollectionAgent)
              .doc(fireStoreId)
              .collection('forms')
              .get(const GetOptions(source: Source.server));

          if (querySnapshot.docs.isNotEmpty) {
            List<RecruitmentFormModel> forms =
                querySnapshot.docs.map((doc) {
                  Map<String, dynamic> data =
                      doc.data() as Map<String, dynamic>;
                  // Tambahkan ID dokumen ke model
                  data['id'] = doc.id;
                  return RecruitmentFormModel.fromJson(data);
                }).toList();

            _logMessage(
              'Successfully retrieved ${forms.length} forms from server',
            );
            return forms;
          } else {
            _logMessage('No forms found on server');
          }
        } catch (serverError) {
          _logMessage('Error retrieving forms from server: $serverError');
        }
      }

      // Sebagai upaya terakhir, coba dengan opsi default
      try {
        QuerySnapshot querySnapshot =
            await db
                .collection(kFireCollectionAgent)
                .doc(fireStoreId)
                .collection('forms')
                .get();

        if (querySnapshot.docs.isNotEmpty) {
          List<RecruitmentFormModel> forms =
              querySnapshot.docs.map((doc) {
                Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
                // Tambahkan ID dokumen ke model
                data['id'] = doc.id;
                return RecruitmentFormModel.fromJson(data);
              }).toList();

          _logMessage(
            'Successfully retrieved ${forms.length} forms with default options',
          );
          return forms;
        }
      } catch (e) {
        _logMessage('Error retrieving forms with default options: $e');
      }

      _logMessage('No forms found in cache or server');
      return [];
    } catch (e) {
      _logMessage('Error in getAllRecruitmentForms: $e');
      return [];
    }
  }

  // Mengambil form dengan status tertentu dari Firestore
  Future<List<RecruitmentFormModel>> getRecruitmentFormsByStatus(
    String status,
  ) async {
    try {
      // Dapatkan fireStoreId dari SharedPreferences
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      String fireStoreId = prefs.getString(kStorageUserFirestoreId) ?? '';

      if (fireStoreId.isEmpty) {
        _logMessage('No Firestore ID available for retrieving forms by status');
        return [];
      }

      _logMessage('Retrieving forms with status: $status');

      // Selalu coba cache terlebih dahulu
      try {
        _logMessage('Trying cache first for forms with status: $status');
        QuerySnapshot querySnapshot = await db
            .collection(kFireCollectionAgent)
            .doc(fireStoreId)
            .collection('forms')
            .where('formStatus', isEqualTo: status)
            .get(const GetOptions(source: Source.cache));

        if (querySnapshot.docs.isNotEmpty) {
          List<RecruitmentFormModel> forms =
              querySnapshot.docs.map((doc) {
                Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
                // Tambahkan ID dokumen ke model
                data['id'] = doc.id;
                return RecruitmentFormModel.fromJson(data);
              }).toList();

          _logMessage(
            'Successfully retrieved ${forms.length} forms with status $status from cache',
          );
          return forms;
        } else {
          _logMessage('No forms with status $status found in cache');
        }
      } catch (cacheError) {
        _logMessage(
          'Error retrieving forms with status $status from cache: $cacheError',
        );
      }

      // Jika cache gagal dan kita online, coba dari server
      final hasConnection = await _checkInternetConnection();
      if (hasConnection) {
        try {
          _logMessage('Trying server for forms with status: $status');
          QuerySnapshot querySnapshot = await db
              .collection(kFireCollectionAgent)
              .doc(fireStoreId)
              .collection('forms')
              .where('formStatus', isEqualTo: status)
              .get(const GetOptions(source: Source.server));

          if (querySnapshot.docs.isNotEmpty) {
            List<RecruitmentFormModel> forms =
                querySnapshot.docs.map((doc) {
                  Map<String, dynamic> data =
                      doc.data() as Map<String, dynamic>;
                  // Tambahkan ID dokumen ke model
                  data['id'] = doc.id;
                  return RecruitmentFormModel.fromJson(data);
                }).toList();

            _logMessage(
              'Successfully retrieved ${forms.length} forms with status $status from server',
            );
            return forms;
          } else {
            _logMessage('No forms with status $status found on server');
          }
        } catch (serverError) {
          _logMessage(
            'Error retrieving forms with status $status from server: $serverError',
          );
        }
      }

      // Sebagai upaya terakhir, coba dengan opsi default
      try {
        QuerySnapshot querySnapshot =
            await db
                .collection(kFireCollectionAgent)
                .doc(fireStoreId)
                .collection('forms')
                .where('formStatus', isEqualTo: status)
                .get();

        if (querySnapshot.docs.isNotEmpty) {
          List<RecruitmentFormModel> forms =
              querySnapshot.docs.map((doc) {
                Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
                // Tambahkan ID dokumen ke model
                data['id'] = doc.id;
                return RecruitmentFormModel.fromJson(data);
              }).toList();

          _logMessage(
            'Successfully retrieved ${forms.length} forms with status $status with default options',
          );
          return forms;
        }
      } catch (e) {
        _logMessage(
          'Error retrieving forms with status $status with default options: $e',
        );
      }

      _logMessage('No forms with status $status found in cache or server');
      return [];
    } catch (e) {
      _logMessage('Error in getRecruitmentFormsByStatus: $e');
      return [];
    }
  }

  // Memindahkan form dari ID lama ke ID baru di Firestore
  Future<bool> moveRecruitmentForm(String oldFormId, String newFormId) async {
    try {
      // Dapatkan fireStoreId dari SharedPreferences
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      String fireStoreId = prefs.getString(kStorageUserFirestoreId) ?? '';

      if (fireStoreId.isEmpty) {
        _logMessage('No Firestore ID available for moving form');
        return false;
      }

      _logMessage('Moving form from ID: $oldFormId to ID: $newFormId');

      // Ambil data dari form lama
      final oldFormData = await getRecruitmentForm(oldFormId);
      if (oldFormData == null) {
        _logMessage('Old form data not found for ID: $oldFormId');
        return false;
      }

      // Update data dengan menandai bahwa form sudah mendapat UUID dari server
      final updatedFormData = oldFormData.copyWith(
        hasServerUuid: true,
        lastUpdated: DateTime.now().millisecondsSinceEpoch,
      );

      // Simpan data ke form ID baru
      final saveResult = await saveRecruitmentForm(updatedFormData, newFormId);
      if (!saveResult) {
        _logMessage('Failed to save form data to new ID: $newFormId');
        return false;
      }

      // Hapus form lama setelah berhasil menyimpan ke ID baru
      final deleteResult = await deleteRecruitmentForm(oldFormId);
      if (!deleteResult) {
        _logMessage('Warning: Failed to delete old form with ID: $oldFormId');
        // Tidak return false karena data sudah berhasil dipindah
      }

      _logMessage('Successfully moved form from $oldFormId to $newFormId');
      return true;
    } catch (e) {
      _logMessage('Error moving form: $e');
      return false;
    }
  }

  // Menghapus form dari Firestore
  Future<bool> deleteRecruitmentForm(String formId) async {
    try {
      // Dapatkan fireStoreId dari SharedPreferences
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      String fireStoreId = prefs.getString(kStorageUserFirestoreId) ?? '';

      if (fireStoreId.isEmpty) {
        _logMessage('No Firestore ID available for deleting form');
        return false;
      }

      // Cek koneksi internet
      bool isOnline = await _checkInternetConnection();
      if (!isOnline) {
        _logMessage('Cannot delete form while offline');
        return false;
      }

      // Hapus dokumen
      await db
          .collection(kFireCollectionAgent)
          .doc(fireStoreId)
          .collection('forms')
          .doc(formId)
          .delete();

      _logMessage('Successfully deleted form with ID: $formId');
      return true;
    } catch (e) {
      _logMessage('Error deleting form: $e');
      return false;
    }
  }

  // Mengecek apakah form sudah memiliki UUID dari server
  Future<bool> hasFormServerUuid(String formId) async {
    try {
      final formData = await getRecruitmentForm(formId);
      if (formData != null) {
        return formData.hasServerUuid ?? false;
      }
      return false;
    } catch (e) {
      _logMessage('Error checking form server UUID: $e');
      return false;
    }
  }

  // Mengambil form berdasarkan status hasServerUuid
  Future<List<RecruitmentFormModel>> getRecruitmentFormsByServerUuidStatus(
    bool hasServerUuid,
  ) async {
    try {
      // Dapatkan fireStoreId dari SharedPreferences
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      String fireStoreId = prefs.getString(kStorageUserFirestoreId) ?? '';

      if (fireStoreId.isEmpty) {
        _logMessage(
          'No Firestore ID available for retrieving forms by server UUID status',
        );
        return [];
      }

      _logMessage('Retrieving forms with hasServerUuid: $hasServerUuid');

      // Selalu coba cache terlebih dahulu
      try {
        _logMessage(
          'Trying cache first for forms with hasServerUuid: $hasServerUuid',
        );
        QuerySnapshot querySnapshot = await db
            .collection(kFireCollectionAgent)
            .doc(fireStoreId)
            .collection('forms')
            .where('hasServerUuid', isEqualTo: hasServerUuid)
            .get(const GetOptions(source: Source.cache));

        if (querySnapshot.docs.isNotEmpty) {
          List<RecruitmentFormModel> forms =
              querySnapshot.docs.map((doc) {
                Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
                // Tambahkan ID dokumen ke model
                data['id'] = doc.id;
                return RecruitmentFormModel.fromJson(data);
              }).toList();

          _logMessage(
            'Successfully retrieved ${forms.length} forms with hasServerUuid $hasServerUuid from cache',
          );
          return forms;
        } else {
          _logMessage(
            'No forms with hasServerUuid $hasServerUuid found in cache',
          );
        }
      } catch (cacheError) {
        _logMessage(
          'Error retrieving forms with hasServerUuid $hasServerUuid from cache: $cacheError',
        );
      }

      // Jika cache gagal dan kita online, coba dari server
      final hasConnection = await _checkInternetConnection();
      if (hasConnection) {
        try {
          _logMessage(
            'Trying server for forms with hasServerUuid: $hasServerUuid',
          );
          QuerySnapshot querySnapshot = await db
              .collection(kFireCollectionAgent)
              .doc(fireStoreId)
              .collection('forms')
              .where('hasServerUuid', isEqualTo: hasServerUuid)
              .get(const GetOptions(source: Source.server));

          if (querySnapshot.docs.isNotEmpty) {
            List<RecruitmentFormModel> forms =
                querySnapshot.docs.map((doc) {
                  Map<String, dynamic> data =
                      doc.data() as Map<String, dynamic>;
                  // Tambahkan ID dokumen ke model
                  data['id'] = doc.id;
                  return RecruitmentFormModel.fromJson(data);
                }).toList();

            _logMessage(
              'Successfully retrieved ${forms.length} forms with hasServerUuid $hasServerUuid from server',
            );
            return forms;
          } else {
            _logMessage(
              'No forms with hasServerUuid $hasServerUuid found on server',
            );
          }
        } catch (serverError) {
          _logMessage(
            'Error retrieving forms with hasServerUuid $hasServerUuid from server: $serverError',
          );
        }
      }

      // Sebagai upaya terakhir, coba dengan opsi default
      try {
        QuerySnapshot querySnapshot =
            await db
                .collection(kFireCollectionAgent)
                .doc(fireStoreId)
                .collection('forms')
                .where('hasServerUuid', isEqualTo: hasServerUuid)
                .get();

        if (querySnapshot.docs.isNotEmpty) {
          List<RecruitmentFormModel> forms =
              querySnapshot.docs.map((doc) {
                Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
                // Tambahkan ID dokumen ke model
                data['id'] = doc.id;
                return RecruitmentFormModel.fromJson(data);
              }).toList();

          _logMessage(
            'Successfully retrieved ${forms.length} forms with hasServerUuid $hasServerUuid with default options',
          );
          return forms;
        }
      } catch (e) {
        _logMessage(
          'Error retrieving forms with hasServerUuid $hasServerUuid with default options: $e',
        );
      }

      _logMessage(
        'No forms with hasServerUuid $hasServerUuid found in cache or server',
      );
      return [];
    } catch (e) {
      _logMessage('Error in getRecruitmentFormsByServerUuidStatus: $e');
      return [];
    }
  }

  // Cek koneksi internet
  Future<bool> _checkInternetConnection() async {
    try {
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } on SocketException catch (_) {
      return false;
    }
  }

  // Fungsi untuk logging
  void _logMessage(String message) {
    try {
      Get.find<LoggerService>().log(message);
    } catch (_) {
      log(message); // Fallback ke log biasa jika LoggerService tidak tersedia
    }
  }
}
