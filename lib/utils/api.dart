import 'dart:io';
import 'dart:developer';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_api.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/utils/config_reader.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:shared_preferences/shared_preferences.dart';

String baseUrl = ConfigReader.getBaseUrl();
String commonUrl = ConfigReader.getPublicUrl();

class Api extends BaseApi {
  // declare URL
  final String _loginUrl = '$baseUrl/auth/login';
  final String _auth = '$baseUrl/auth';
  final String _profileUrl = '$baseUrl/profile';
  final String _device = '$baseUrl/device';
  // Widgets URL
  final String _widget = '$baseUrl/widget';
  final String _promosiAgent = '$baseUrl/widget/promosi-agent';
  final String _promosiLeader = '$baseUrl/widget/promosi-leader';
  final String _validasiHirarki = '$baseUrl/widget/validasi-hirarki';
  final String _validasiG1 = '$baseUrl/widget/validasi-g1';
  final String _commission = '$baseUrl/widget/commission';
  final String _detailCommission = '$baseUrl/widget/detail-commission';
  final String _persistencyIndividu = '$baseUrl/widget/persistency-individual';
  final String _persistencyTeam = '$baseUrl/widget/persistency-team';
  final String _menuUrl = '$commonUrl/menu';

  // public URL
  final String _flyer = '$commonUrl/public/flyer';
  final String _information = '$commonUrl/public/information';
  final String _question = '$commonUrl/public/question';
  final String _theme = '$commonUrl/public/theme/current';

  // cms URL
  final String _cms = '$baseUrl/cms';

  // Recruitment
  final String _recruitment = '$baseUrl/agency/recruitment';
  final String _comboCategory = '$commonUrl/public/combo-category';
  final String _branch = '$baseUrl/branch';
  final String _bank = '$baseUrl/bank';

  Future<void> performLogin({
    required BaseControllers controllers,
    required var data,
    int? code,
  }) async {
    await apiPost(
      url: _loginUrl,
      controller: controllers,
      data: data,
      debug: false,
      code: code ?? 0,
    );
  }

  // Auth
  Future<void> performForgotPassword({
    required BaseControllers controllers,
    required var data,
    int? code,
  }) async {
    await apiPost(
      url: '$_auth/forget-password',
      controller: controllers,
      data: data,
      debug: false,
      code: code ?? 0,
    );
  }

  Future<void> performResetPassword({
    required BaseControllers controllers,
    required var data,
    required String token,
    int? code,
  }) async {
    await apiPost(
      url: '$_auth/reset-password/$token',
      controller: controllers,
      data: data,
      debug: false,
      code: code ?? 0,
    );
  }

  // Profile
  Future<void> getProfile({
    required BaseControllers controllers,
    int? code,
    bool? debug,
  }) async {
    await apiFetch(
      url: _profileUrl,
      controller: controllers,
      debug: false,
      code: code ?? 0,
    );
  }

  Future<void> patchProfile({
    required BaseControllers controllers,
    var data,
    int? code,
  }) async {
    await apiPatch(
      url: '$_profileUrl/request',
      controller: controllers,
      data: data,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> updateProfileCms({
    required BaseControllers controllers,
    var data,
    int? code,
  }) async {
    await apiPost(
      url: '$_cms/profile',
      controller: controllers,
      data: data,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getProfileRequestStatus({
    required BaseControllers controllers,
    int? code,
  }) async {
    await apiFetch(
      url: '$_profileUrl/request',
      controller: controllers,
      debug: false,
      code: code ?? 0,
    );
  }

  Future<void> performChangePassword({
    required BaseControllers controllers,
    var data,
    int? code,
  }) async {
    await apiPost(
      url: '$_profileUrl/change-password',
      controller: controllers,
      data: data,
      code: code ?? 0,
      debug: false,
    );
  }

  // Device
  Future<void> getDeviceList({
    required BaseControllers controllers,
    int? code,
  }) async {
    await apiFetch(
      url: _device,
      controller: controllers,
      debug: false,
      code: code ?? 0,
    );
  }

  Future<void> performRegisterDevice({
    required BaseControllers controllers,
    var data,
    int? code,
  }) async {
    await apiPost(
      url: '$_device/register',
      controller: controllers,
      data: data,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> performRevokeDevice({
    required BaseControllers controllers,
    required String deviceId,
    int? code,
  }) async {
    await apiPost(
      url: '$_device/revoke/$deviceId',
      controller: controllers,
      data: {},
      code: code ?? 0,
      debug: false,
    );
  }

  // Public
  Future<void> getFlyer({
    required BaseControllers controllers,
    int? code,
  }) async {
    await apiFetch(
      url: '$_flyer?isAvtive=true',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getInformation({
    required BaseControllers controllers,
    required String infoType,
    int? code,
  }) async {
    await apiFetch(
      url: '$_information?informationType=$infoType',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getQuestion({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_question?$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  // Get current theme
  Future<void> getTheme({
    required BaseControllers controllers,
    int? code,
  }) async {
    await apiFetch(
      url: _theme,
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getPromosiAgent({
    required BaseControllers controllers,
    String? params,
    int? code,
    String? userLevel,
  }) async {
    // Use promosi-leader endpoint for BD level users
    String endpoint =
        [kLevelBD, kLevelBM].contains(userLevel)
            ? _promosiLeader
            : _promosiAgent;
    await apiFetch(
      url: '$endpoint?$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getValidasiHirarki({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_validasiHirarki?$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getValidasiG1({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_validasiG1?$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getCommission({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_commission${params != null ? '?$params' : ''}',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getDetailCommission({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_detailCommission${params != null ? '?$params' : ''}',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  // Strat OF Widget API
  Future<void> getWidgetProductionSum({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_widget/summary-production?$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getWidgetProductionDetail({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_widget/detail-production?$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getWidgetProductionMy({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_widget/my-production?$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getWidgetProductionTeam({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_widget/team-production?$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getWidgetProductionGroup({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_widget/group-production?$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getWidgetProductionBranch({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_widget/branch-production?$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getWidgetProductionAreaBdm({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_widget/bdm-production?$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getWidgetProductionAreaAbdd({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_widget/abdd-production?$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getWidgetProductionAreaBdd({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_widget/bdd-production?$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getWidgetProductionAreaHos({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_widget/hos-production?$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getProductionGraphicDataYear({
    required BaseControllers controllers,
    int? code,
  }) async {
    await apiFetch(
      url: '$_widget/yearly-summary-production',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getProductionGraphicDataMonth({
    required BaseControllers controllers,
    int? code,
  }) async {
    await apiFetch(
      url: '$_widget/monthly-summary-production?year=${DateTime.now().year}',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  // Persistency API
  Future<void> getPersistencyIndividu({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_persistencyIndividu?$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getPersistencyTeam({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_persistencyTeam?$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getSpajIndividu({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_widget/spaj-individual?${params ?? ''}',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getSpajTeam({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_widget/spaj-team?${params ?? ''}',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getStatusClaim({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_widget/claim-tracking?$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getPolicyLapsed({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_widget/policy-lapsed?$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getPolicyOverdue({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_widget/policy-overdue?$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getBirthdayCustomer({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_widget/birthday-customer?$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getBirthdayTemplate({
    required BaseControllers controllers,
    int? code,
  }) async {
    await apiFetch(
      url: '$commonUrl/globalConfig/birthday.template',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getWidgetSort({
    required BaseControllers controllers,
    int? code,
  }) async {
    await apiFetch(
      url: '$_menuUrl?menuType=WIDGET&channel=AGE&isActive=true',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  // Form
  Future<void> getBranch({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_branch?$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getBank({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_bank?$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  // Combo
  Future<void> getComboCategory({
    required BaseControllers controllers,
    int? code,
  }) async {
    await apiFetch(
      url: _comboCategory,
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getComboCategoryById({
    required BaseControllers controllers,
    required String key,
    int? code,
    bool? debug,
  }) async {
    await apiFetch(
      url: '$_comboCategory/$key',
      controller: controllers,
      code: code ?? 0,
      debug: debug ?? false,
    );
  }

  // Recruitment
  Future<Map<String, dynamic>?> uploadRecruitmentImage({
    required File imageFile,
    required String type,
    Function(double)? onProgress,
  }) async {
    try {
      // Check internet connection first
      final hasConnection = await _checkInternetConnection();
      if (!hasConnection) {
        log("Tidak ada koneksi internet untuk upload gambar");
        return null;
      }

      // Create form data
      final form = FormData({
        'file': MultipartFile(
          imageFile,
          filename: imageFile.path.split('/').last,
        ),
      });

      // Get token
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      String token = prefs.getString(kStorageToken) ?? '';

      // Upload to API
      final response = await GetConnect().post(
        '$_recruitment/upload/$type',
        form,
        headers: {'Authorization': 'Bearer $token'},
        uploadProgress: (percent) {
          if (onProgress != null) {
            onProgress(percent);
          }
        },
      );

      if (response.isOk && response.body != null) {
        // Parse response to get URL
        final responseData = response.body;
        String? uploadedUrl;

        // Handle different response formats
        if (responseData is Map<String, dynamic>) {
          uploadedUrl = response.body['initialPreview'][0];
        } else if (responseData is String) {
          uploadedUrl = response.body['initialPreview'][0];
        }

        if (uploadedUrl != null && uploadedUrl.isNotEmpty) {
          log("Upload berhasil untuk $type: $uploadedUrl");
          return {
            'success': true,
            'url': uploadedUrl,
            'message': 'Upload berhasil',
          };
        } else {
          log("Upload gagal: URL tidak ditemukan dalam response");
          return {
            'success': false,
            'url': null,
            'message': 'URL tidak ditemukan dalam response',
          };
        }
      } else {
        log("Upload gagal: ${response.statusText}");
        return {
          'success': false,
          'url': null,
          'message': response.statusText ?? 'Upload gagal',
        };
      }
    } catch (e) {
      log("Error saat upload gambar $type: $e");
      return {'success': false, 'url': null, 'message': 'Error: $e'};
    }
  }

  // Check internet connection
  Future<bool> _checkInternetConnection() async {
    try {
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } on SocketException catch (_) {
      return false;
    }
  }

  Future<void> getRecruitmentList({
    required BaseControllers controllers,
    int? code,
  }) async {
    await apiFetch(
      url: _recruitment,
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> performSaveDraft({
    required BaseControllers controllers,
    required var data,
    int? code,
  }) async {
    await apiPost(
      url: '$_recruitment/draft',
      controller: controllers,
      data: data,
      debug: false,
      code: code ?? 0,
    );
  }

  Future<void> performSubmitRecruitmentForm({
    required BaseControllers controllers,
    required var data,
    int? code,
  }) async {
    await apiPost(
      url: '$_recruitment/submit',
      controller: controllers,
      data: data,
      debug: false,
      code: code ?? 0,
    );
  }

  // Send verification email
  Future<void> performSendVerifEmail({
    required BaseControllers controllers,
    required String uuid,
    int? code,
  }) async {
    await apiPost(
      url: '$_recruitment/$uuid/send-verification-email',
      controller: controllers,
      data: {},
      debug: false,
      code: code ?? 0,
    );
  }

  Future<void> getIsVerified({
    required BaseControllers controllers,
    required String uuid,
    int? code,
  }) async {
    await apiFetch(
      url: '$_recruitment/check-email-verified?uuid=$uuid',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }
}
