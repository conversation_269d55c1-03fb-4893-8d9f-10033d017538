import 'package:pdl_superapp/models/branch_models.dart';

class RecruitmentDraftModels {
  int? id;
  String? uuid;
  Recruiter? recruiter;
  ApprovalHeader? approvalHeader;
  String? approvalStatus;
  String? trxStatus;
  String? recruiterCode;
  String? recruiterName;
  String? recruiterBranchCode;
  String? leaderCode;
  String? agentCode;
  String? ktpPhoto;
  String? selfiePhoto;
  String? passPhoto;
  String? positionLevel;
  BranchModels? branch;
  String? nik;
  String? fullName;
  String? birthPlace;
  String? birthDate;
  String? gender;
  String? ktpProvince;
  String? ktpCity;
  String? ktpDistrict;
  String? ktpSubDistrict;
  String? ktpRt;
  String? ktpRw;
  String? ktpAddress;
  bool? isDomicileSameAsKtp;
  String? domicileProvince;
  String? domicileCity;
  String? domicileDistrict;
  String? domicileSubDistrict;
  String? domicileRt;
  String? domicileRw;
  String? domicileAddress;
  String? phoneNumber;
  String? maritalStatus;
  String? occupation;
  String? occupationCode;
  String? email;
  String? emergencyContactName;
  String? emergencyContactRelation;
  String? emergencyContactPhone;
  String? bankAccountName;
  String? bankAccountNumber;
  Bank? bank;
  String? lastJob;
  String? last5YearJob;
  String? last2YearProduction;
  String? lastCompanyManPower;
  String? rewardInfo;
  String? pkajFile;
  String? pmkajFile;
  String? kodeEtikFile;
  String? antiTwistingFile;
  String? signature;
  String? paraf;
  bool? isEmailVerified;
  String? emailVerifiedDate;
  String? validationBlacklistStatus;
  String? validationKtpStatus;
  String? resulValidationKtp;
  String? validationBankAccountStatus;
  String? resultValidationBankAccount;
  String? validationHirarkiStatus;
  String? resultValidationHirarki;
  String? validationAmlStatus;
  String? validationAdministrationAgentStatus;
  String? validationLicenseAajiStatus;
  String? validationLicenseAasiStatus;
  String? resultInterview;
  String? createdAt;
  String? updatedAt;

  RecruitmentDraftModels();

  RecruitmentDraftModels.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    uuid = json['uuid'];
    recruiter =
        json['recruiter'] != null
            ? Recruiter.fromJson(json['recruiter'])
            : null;
    approvalHeader =
        json['approvalHeader'] != null
            ? ApprovalHeader.fromJson(json['approvalHeader'])
            : null;
    approvalStatus = json['approvalStatus'];
    trxStatus = json['trxStatus'];
    recruiterCode = json['recruiterCode'];
    recruiterName = json['recruiterName'];
    recruiterBranchCode = json['recruiterBranchCode'];
    leaderCode = json['leaderCode'];
    agentCode = json['agentCode'];
    ktpPhoto = json['ktpPhoto'];
    selfiePhoto = json['selfiePhoto'];
    passPhoto = json['passPhoto'];
    positionLevel = json['positionLevel'];
    branch =
        json['branch'] != null ? BranchModels.fromJson(json['branch']) : null;
    nik = json['nik'];
    fullName = json['fullName'];
    birthPlace = json['birthPlace'];
    birthDate = json['birthDate'];
    gender = json['gender'];
    ktpProvince = json['ktpProvince'];
    ktpCity = json['ktpCity'];
    ktpDistrict = json['ktpDistrict'];
    ktpSubDistrict = json['ktpSubDistrict'];
    ktpRt = json['ktpRt'];
    ktpRw = json['ktpRw'];
    ktpAddress = json['ktpAddress'];
    isDomicileSameAsKtp = json['isDomicileSameAsKtp'];
    domicileProvince = json['domicileProvince'];
    domicileCity = json['domicileCity'];
    domicileDistrict = json['domicileDistrict'];
    domicileSubDistrict = json['domicileSubDistrict'];
    domicileRt = json['domicileRt'];
    domicileRw = json['domicileRw'];
    domicileAddress = json['domicileAddress'];
    phoneNumber = json['phoneNumber'];
    maritalStatus = json['maritalStatus'];
    occupation = json['occupation'];
    occupationCode = json['occupationCode'];
    email = json['email'];
    emergencyContactName = json['emergencyContactName'];
    emergencyContactRelation = json['emergencyContactRelation'];
    emergencyContactPhone = json['emergencyContactPhone'];
    bankAccountName = json['bankAccountName'];
    bankAccountNumber = json['bankAccountNumber'];
    bank = json['bank'] != null ? Bank.fromJson(json['bank']) : null;
    lastJob = json['lastJob'];
    last5YearJob = json['last5YearJob'];
    last2YearProduction = json['last2YearProduction'];
    lastCompanyManPower = json['lastCompanyManPower'];
    rewardInfo = json['rewardInfo'];
    pkajFile = json['pkajFile'];
    pmkajFile = json['pmkajFile'];
    kodeEtikFile = json['kodeEtikFile'];
    antiTwistingFile = json['antiTwistingFile'];
    signature = json['signature'];
    paraf = json['paraf'];
    isEmailVerified = json['isEmailVerified'];
    emailVerifiedDate = json['emailVerifiedDate'];
    validationBlacklistStatus = json['validationBlacklistStatus'];
    validationKtpStatus = json['validationKtpStatus'];
    resulValidationKtp = json['resulValidationKtp'];
    validationBankAccountStatus = json['validationBankAccountStatus'];
    resultValidationBankAccount = json['resultValidationBankAccount'];
    validationHirarkiStatus = json['validationHirarkiStatus'];
    resultValidationHirarki = json['resultValidationHirarki'];
    validationAmlStatus = json['validationAmlStatus'];
    validationAdministrationAgentStatus =
        json['validationAdministrationAgentStatus'];
    validationLicenseAajiStatus = json['validationLicenseAajiStatus'];
    validationLicenseAasiStatus = json['validationLicenseAasiStatus'];
    resultInterview = json['resultInterview'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
  }
}

// Model turunan
class Recruiter {
  String? username;
  String? name;
  String? channel;
  String? agentCode;
  String? agentLevel;
  List<BranchModels>? branches;

  Recruiter();

  Recruiter.fromJson(Map<String, dynamic> json) {
    username = json['username'];
    name = json['name'];
    channel = json['channel'];
    agentCode = json['agentCode'];
    agentLevel = json['agentLevel'];
    if (json['branches'] != null) {
      branches =
          List.from(
            json['branches'],
          ).map((e) => BranchModels.fromJson(e)).toList();
    }
  }
}

class ApprovalHeader {
  int? id;
  String? requestId;
  Recruiter? requestBy;
  String? trxType;
  int? trxId;
  String? approvalStatus;
  int? currentLevel;
  String? approverRole;
  int? maxLevel;
  String? remarks;
  List<ApprovalDetail>? approvalDetails;
  String? detailApproval;
  String? lastApproverRole;
  Map<String, dynamic>? detailData;
  int? lastLevel;
  String? createdAt;
  String? updatedAt;

  ApprovalHeader();

  ApprovalHeader.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    requestId = json['requestId'];
    requestBy =
        json['requestBy'] != null
            ? Recruiter.fromJson(json['requestBy'])
            : null;
    trxType = json['trxType'];
    trxId = json['trxId'];
    approvalStatus = json['approvalStatus'];
    currentLevel = json['currentLevel'];
    approverRole = json['approverRole'];
    maxLevel = json['maxLevel'];
    remarks = json['remarks'];
    if (json['approvalDetails'] != null) {
      approvalDetails =
          List.from(
            json['approvalDetails'],
          ).map((e) => ApprovalDetail.fromJson(e)).toList();
    }
    detailApproval = json['detailApproval'];
    lastApproverRole = json['lastApproverRole'];
    detailData = json['detailData'];
    lastLevel = json['lastLevel'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
  }
}

class ApprovalDetail {
  int? id;
  Recruiter? actionBy;
  String? remarks;
  String? approvalStatus;
  String? detailApproval;
  int? levelNumber;
  String? createdAt;

  ApprovalDetail();

  ApprovalDetail.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    actionBy =
        json['actionBy'] != null ? Recruiter.fromJson(json['actionBy']) : null;
    remarks = json['remarks'];
    approvalStatus = json['approvalStatus'];
    detailApproval = json['detailApproval'];
    levelNumber = json['levelNumber'];
    createdAt = json['createdAt'];
  }
}

class Bank {
  int? id;
  String? bankCode;
  String? bankAdvanceAiCode;
  String? bankName;
  String? createdAt;
  String? updatedAt;

  Bank();

  Bank.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    bankCode = json['bankCode'];
    bankAdvanceAiCode = json['bankAdvanceAiCode'];
    bankName = json['bankName'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
  }
}
