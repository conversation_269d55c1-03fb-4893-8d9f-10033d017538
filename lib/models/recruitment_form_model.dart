class RecruitmentFormModel {
  final String? id; // Document ID di Firestore
  final String? nik;
  final String? namaKtp;
  final String? tempatLahir;
  final String? tanggalLahir;
  final String? bulanLahir;
  final String? tahunLahir;
  final String? jenisKelamin;
  final String? alamatKtp;
  final String? rtKtp;
  final String? rwKtp;
  final String? provinsiKtp;
  final String? kabupatenKtp;
  final String? kecamatanKtp;
  final String? kelurahanKtp;
  final String? maritalStatus;

  // Alamat Domisili
  final String? alamatDomisili;
  final String? rtDomisili;
  final String? rwDomisili;
  final String? provinsiDomisili;
  final String? kabupatenDomisili;
  final String? kecamatanDomisili;
  final String? kelurahanDomisili;

  // Data Pribadi
  final String? email;
  final String? nomorHp;
  final String? occupation;
  final String? occupationCode;

  // Emergency Contact
  final String? emergencyNama;
  final String? emergencyHubungan;
  final String? emergencyNomorHp;

  // Bank
  final String? namaPemilikRekening;
  final String? nomorRekening;
  final String? namaBank;
  final int? bankCode;

  // Foto
  final String? ktpImagePath;
  final String? selfieKtpImagePath;
  final String? pasFotoImagePath;

  // Foto URLs
  final String? ktpImageUrl;
  final String? selfieKtpImageUrl;
  final String? pasFotoImageUrl;

  // Metadata
  final int? lastUpdated;
  final bool? isSubmitted;
  final String? formStatus; // draft, submitted, approved, rejected
  final bool? hasServerUuid; // true jika form sudah mendapat UUID dari server

  // Recruiter Info
  final String? recruiterName;
  final String? recruiterId;
  final String? recruiterBranch;
  final String? recruiterCode;
  final String? recruiterLevel;
  final String? recruiterPhoto;

  // candidate Info
  final String? candidateLevel;
  final String? candidateBranch;
  final int? candidateBranchCode;

  // Terms and Signature
  final String? signature;
  final String? paraf;

  // Signature and Paraf URLs
  final String? signatureUrl;
  final String? parafUrl;

  RecruitmentFormModel({
    this.id,
    this.nik,
    this.namaKtp,
    this.tempatLahir,
    this.tanggalLahir,
    this.bulanLahir,
    this.tahunLahir,
    this.jenisKelamin,
    this.alamatKtp,
    this.rtKtp,
    this.rwKtp,
    this.provinsiKtp,
    this.kabupatenKtp,
    this.kecamatanKtp,
    this.kelurahanKtp,
    this.maritalStatus,
    this.alamatDomisili,
    this.rtDomisili,
    this.rwDomisili,
    this.provinsiDomisili,
    this.kabupatenDomisili,
    this.kecamatanDomisili,
    this.kelurahanDomisili,
    this.email,
    this.nomorHp,
    this.occupation,
    this.occupationCode,
    this.emergencyNama,
    this.emergencyHubungan,
    this.emergencyNomorHp,
    this.namaPemilikRekening,
    this.nomorRekening,
    this.namaBank,
    this.bankCode,
    this.ktpImagePath,
    this.selfieKtpImagePath,
    this.pasFotoImagePath,
    this.ktpImageUrl,
    this.selfieKtpImageUrl,
    this.pasFotoImageUrl,
    this.lastUpdated,
    this.isSubmitted,
    this.formStatus,
    this.hasServerUuid,
    this.recruiterName,
    this.recruiterId,
    this.recruiterBranch,
    this.recruiterCode,
    this.recruiterLevel,
    this.recruiterPhoto,
    this.candidateLevel,
    this.candidateBranch,
    this.candidateBranchCode,
    this.signature,
    this.paraf,
    this.signatureUrl,
    this.parafUrl,
  });

  factory RecruitmentFormModel.fromJson(Map<String, dynamic> json) {
    return RecruitmentFormModel(
      id: json['id'],
      nik: json['nik'],
      namaKtp: json['namaKtp'],
      tempatLahir: json['tempatLahir'],
      tanggalLahir: json['tanggalLahir'],
      bulanLahir: json['bulanLahir'],
      tahunLahir: json['tahunLahir'],
      jenisKelamin: json['jenisKelamin'],
      alamatKtp: json['alamatKtp'],
      rtKtp: json['rtKtp'],
      rwKtp: json['rwKtp'],
      provinsiKtp: json['provinsiKtp'],
      kabupatenKtp: json['kabupatenKtp'],
      kecamatanKtp: json['kecamatanKtp'],
      kelurahanKtp: json['kelurahanKtp'],
      maritalStatus: json['maritalStatus'],
      alamatDomisili: json['alamatDomisili'],
      rtDomisili: json['rtDomisili'],
      rwDomisili: json['rwDomisili'],
      provinsiDomisili: json['provinsiDomisili'],
      kabupatenDomisili: json['kabupatenDomisili'],
      kecamatanDomisili: json['kecamatanDomisili'],
      kelurahanDomisili: json['kelurahanDomisili'],
      email: json['email'],
      nomorHp: json['nomorHp'],
      occupation: json['occupation'],
      occupationCode: json['occupationCode'],
      emergencyNama: json['emergencyNama'],
      emergencyHubungan: json['emergencyHubungan'],
      emergencyNomorHp: json['emergencyNomorHp'],
      namaPemilikRekening: json['namaPemilikRekening'],
      nomorRekening: json['nomorRekening'],
      namaBank: json['namaBank'],
      bankCode: json['bankCode'],
      ktpImagePath: json['ktpImagePath'],
      selfieKtpImagePath: json['selfieKtpImagePath'],
      pasFotoImagePath: json['pasFotoImagePath'],
      ktpImageUrl: json['ktpImageUrl'],
      selfieKtpImageUrl: json['selfieKtpImageUrl'],
      pasFotoImageUrl: json['pasFotoImageUrl'],
      lastUpdated: json['lastUpdated'],
      isSubmitted: json['isSubmitted'],
      formStatus: json['formStatus'],
      hasServerUuid: json['hasServerUuid'],
      recruiterName: json['recruiterName'],
      recruiterId: json['recruiterId'],
      recruiterBranch: json['recruiterBranch'],
      recruiterCode: json['recruiterCode'],
      recruiterLevel: json['recruiterLevel'],
      recruiterPhoto: json['recruiterPhoto'],
      candidateLevel: json['candidateLevel'],
      candidateBranch: json['candidateBranch'],
      candidateBranchCode: json['candidateBranchCode'],
      signature: json['signature'],
      paraf: json['paraf'],
      signatureUrl: json['signatureUrl'],
      parafUrl: json['parafUrl'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nik': nik,
      'namaKtp': namaKtp,
      'tempatLahir': tempatLahir,
      'tanggalLahir': tanggalLahir,
      'bulanLahir': bulanLahir,
      'tahunLahir': tahunLahir,
      'jenisKelamin': jenisKelamin,
      'alamatKtp': alamatKtp,
      'rtKtp': rtKtp,
      'rwKtp': rwKtp,
      'provinsiKtp': provinsiKtp,
      'kabupatenKtp': kabupatenKtp,
      'kecamatanKtp': kecamatanKtp,
      'kelurahanKtp': kelurahanKtp,
      'maritalStatus': maritalStatus,
      'alamatDomisili': alamatDomisili,
      'rtDomisili': rtDomisili,
      'rwDomisili': rwDomisili,
      'provinsiDomisili': provinsiDomisili,
      'kabupatenDomisili': kabupatenDomisili,
      'kecamatanDomisili': kecamatanDomisili,
      'kelurahanDomisili': kelurahanDomisili,
      'email': email,
      'nomorHp': nomorHp,
      'occupation': occupation,
      'occupationCode': occupationCode,
      'emergencyNama': emergencyNama,
      'emergencyHubungan': emergencyHubungan,
      'emergencyNomorHp': emergencyNomorHp,
      'namaPemilikRekening': namaPemilikRekening,
      'nomorRekening': nomorRekening,
      'namaBank': namaBank,
      'bankCode': bankCode,
      'ktpImagePath': ktpImagePath,
      'selfieKtpImagePath': selfieKtpImagePath,
      'pasFotoImagePath': pasFotoImagePath,
      'ktpImageUrl': ktpImageUrl,
      'selfieKtpImageUrl': selfieKtpImageUrl,
      'pasFotoImageUrl': pasFotoImageUrl,
      'lastUpdated': lastUpdated,
      'isSubmitted': isSubmitted,
      'formStatus': formStatus,
      'hasServerUuid': hasServerUuid,
      'recruiterName': recruiterName,
      'recruiterId': recruiterId,
      'recruiterBranch': recruiterBranch,
      'recruiterCode': recruiterCode,
      'recruiterLevel': recruiterLevel,
      'recruiterPhoto': recruiterPhoto,
      'candidateLevel': candidateLevel,
      'candidateBranch': candidateBranch,
      'candidateBranchCode': candidateBranchCode,
      'signature': signature,
      'paraf': paraf,
      'signatureUrl': signatureUrl,
      'parafUrl': parafUrl,
    };
  }

  // Membuat salinan model dengan nilai yang diperbarui
  RecruitmentFormModel copyWith({
    String? id,
    String? nik,
    String? namaKtp,
    String? tempatLahir,
    String? tanggalLahir,
    String? bulanLahir,
    String? tahunLahir,
    String? jenisKelamin,
    String? alamatKtp,
    String? rtKtp,
    String? rwKtp,
    String? provinsiKtp,
    String? kabupatenKtp,
    String? kecamatanKtp,
    String? kelurahanKtp,
    String? maritalStatus,
    String? alamatDomisili,
    String? rtDomisili,
    String? rwDomisili,
    String? provinsiDomisili,
    String? kabupatenDomisili,
    String? kecamatanDomisili,
    String? kelurahanDomisili,
    String? email,
    String? nomorHp,
    String? occupation,
    String? occupationCode,
    String? emergencyNama,
    String? emergencyHubungan,
    String? emergencyNomorHp,
    String? namaPemilikRekening,
    String? nomorRekening,
    String? namaBank,
    int? bankCode,
    String? ktpImagePath,
    String? selfieKtpImagePath,
    String? pasFotoImagePath,
    String? ktpImageUrl,
    String? selfieKtpImageUrl,
    String? pasFotoImageUrl,
    int? lastUpdated,
    bool? isSubmitted,
    String? formStatus,
    bool? hasServerUuid,
    String? recruiterName,
    String? recruiterId,
    String? recruiterBranch,
    String? recruiterCode,
    String? recruiterLevel,
    String? recruiterPhoto,
    String? candidateLevel,
    String? candidateBranch,
    int? candidateBranchCode,
    String? signature,
    String? paraf,
    String? signatureUrl,
    String? parafUrl,
  }) {
    return RecruitmentFormModel(
      id: id ?? this.id,
      nik: nik ?? this.nik,
      namaKtp: namaKtp ?? this.namaKtp,
      tempatLahir: tempatLahir ?? this.tempatLahir,
      tanggalLahir: tanggalLahir ?? this.tanggalLahir,
      bulanLahir: bulanLahir ?? this.bulanLahir,
      tahunLahir: tahunLahir ?? this.tahunLahir,
      jenisKelamin: jenisKelamin ?? this.jenisKelamin,
      alamatKtp: alamatKtp ?? this.alamatKtp,
      rtKtp: rtKtp ?? this.rtKtp,
      rwKtp: rwKtp ?? this.rwKtp,
      provinsiKtp: provinsiKtp ?? this.provinsiKtp,
      kabupatenKtp: kabupatenKtp ?? this.kabupatenKtp,
      kecamatanKtp: kecamatanKtp ?? this.kecamatanKtp,
      kelurahanKtp: kelurahanKtp ?? this.kelurahanKtp,
      maritalStatus: maritalStatus ?? this.maritalStatus,
      alamatDomisili: alamatDomisili ?? this.alamatDomisili,
      rtDomisili: rtDomisili ?? this.rtDomisili,
      rwDomisili: rwDomisili ?? this.rwDomisili,
      provinsiDomisili: provinsiDomisili ?? this.provinsiDomisili,
      kabupatenDomisili: kabupatenDomisili ?? this.kabupatenDomisili,
      kecamatanDomisili: kecamatanDomisili ?? this.kecamatanDomisili,
      kelurahanDomisili: kelurahanDomisili ?? this.kelurahanDomisili,
      email: email ?? this.email,
      nomorHp: nomorHp ?? this.nomorHp,
      occupation: occupation ?? this.occupation,
      occupationCode: occupationCode ?? this.occupationCode,
      emergencyNama: emergencyNama ?? this.emergencyNama,
      emergencyHubungan: emergencyHubungan ?? this.emergencyHubungan,
      emergencyNomorHp: emergencyNomorHp ?? this.emergencyNomorHp,
      namaPemilikRekening: namaPemilikRekening ?? this.namaPemilikRekening,
      nomorRekening: nomorRekening ?? this.nomorRekening,
      namaBank: namaBank ?? this.namaBank,
      bankCode: bankCode ?? this.bankCode,
      ktpImagePath: ktpImagePath ?? this.ktpImagePath,
      selfieKtpImagePath: selfieKtpImagePath ?? this.selfieKtpImagePath,
      pasFotoImagePath: pasFotoImagePath ?? this.pasFotoImagePath,
      ktpImageUrl: ktpImageUrl ?? this.ktpImageUrl,
      selfieKtpImageUrl: selfieKtpImageUrl ?? this.selfieKtpImageUrl,
      pasFotoImageUrl: pasFotoImageUrl ?? this.pasFotoImageUrl,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      isSubmitted: isSubmitted ?? this.isSubmitted,
      formStatus: formStatus ?? this.formStatus,
      hasServerUuid: hasServerUuid ?? this.hasServerUuid,
      recruiterName: recruiterName ?? this.recruiterName,
      recruiterId: recruiterId ?? this.recruiterId,
      recruiterBranch: recruiterBranch ?? this.recruiterBranch,
      recruiterCode: recruiterCode ?? this.recruiterCode,
      recruiterLevel: recruiterLevel ?? this.recruiterLevel,
      recruiterPhoto: recruiterPhoto ?? this.recruiterPhoto,
      candidateLevel: candidateLevel ?? this.candidateLevel,
      candidateBranch: candidateBranch ?? this.candidateBranch,
      candidateBranchCode: candidateBranchCode ?? this.candidateBranchCode,
      signature: signature ?? this.signature,
      paraf: paraf ?? this.paraf,
      signatureUrl: signatureUrl ?? this.signatureUrl,
      parafUrl: parafUrl ?? this.parafUrl,
    );
  }
}
