import 'dart:developer';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/recruitment_form_model.dart';
import 'package:pdl_superapp/models/recruitment_api_model.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/form_firestore_service.dart';
import 'package:pdl_superapp/utils/logger_service.dart';
import 'package:pdl_superapp/utils/keys.dart';

class RecruitmentListController extends BaseControllers {
  // Service untuk Firestore
  final FormFirestoreService _firestoreService = FormFirestoreService();

  // List untuk menyimpan form dengan status draft
  RxList<RecruitmentFormModel> draftForms = <RecruitmentFormModel>[].obs;

  // List untuk menyimpan data dari API recruitment list
  RxList<RecruitmentApiModel> apiRecruitmentList = <RecruitmentApiModel>[].obs;

  // Status loading
  RxBool isLoadingForms = false.obs;
  RxBool isLoadingApiData = false.obs;

  // Filter pencarian
  RxString searchQuery = ''.obs;

  @override
  void onInit() {
    super.onInit();
    fetchDraftForms();
    fetchApiRecruitmentList();
  }

  // Mengambil form dengan status draft
  Future<void> fetchDraftForms() async {
    isLoadingForms.value = true;

    try {
      final forms = await _firestoreService.getRecruitmentFormsByStatus(
        'draft',
      );
      draftForms.value = forms;
    } catch (e) {
      try {
        Get.find<LoggerService>().log('Error fetching draft forms: $e');
      } catch (_) {
        log('Error fetching draft forms: $e');
      }
    } finally {
      isLoadingForms.value = false;
    }
  }

  // Filter form berdasarkan nama
  List<RecruitmentFormModel> get filteredForms {
    if (searchQuery.value.isEmpty) {
      return draftForms;
    }

    return draftForms.where((form) {
      final name = form.namaKtp?.toLowerCase() ?? '';
      return name.contains(searchQuery.value.toLowerCase());
    }).toList();
  }

  // Update filter pencarian
  void updateSearchQuery(String query) {
    searchQuery.value = query;
  }

  // Mengambil data recruitment dari API
  Future<void> fetchApiRecruitmentList() async {
    isLoadingApiData.value = true;
    try {
      await api.getRecruitmentList(
        controllers: this,
        code: kReqGetRecruitmentList,
      );
    } catch (e) {
      try {
        Get.find<LoggerService>().log(
          'Error fetching API recruitment list: $e',
        );
      } catch (_) {
        log('Error fetching API recruitment list: $e');
      }
    }
    // Loading akan di-set false di loadSuccess atau loadFailed
  }

  // Handle API response
  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );

    switch (requestCode) {
      case kReqGetRecruitmentList:
        parseApiRecruitmentList(response);
        break;
      default:
        break;
    }
  }

  // Parse API recruitment list response
  void parseApiRecruitmentList(dynamic response) {
    try {
      apiRecruitmentList.clear();

      // Handle different response structures
      var data = [];
      if (response is Map && response.containsKey('content')) {
        data = response['content'] as List;
      } else if (response is List) {
        data = response;
      }

      for (var item in data) {
        try {
          RecruitmentApiModel recruitment = RecruitmentApiModel.fromJson(item);
          apiRecruitmentList.add(recruitment);
        } catch (e) {
          log('Error parsing recruitment item: $e');
        }
      }

      log(
        'Successfully loaded ${apiRecruitmentList.length} recruitment items from API',
      );
    } catch (e) {
      try {
        Get.find<LoggerService>().log('Error parsing API recruitment list: $e');
      } catch (_) {
        log('Error parsing API recruitment list: $e');
      }
    } finally {
      isLoadingApiData.value = false;
    }
  }

  // Filter API recruitment berdasarkan nama
  List<RecruitmentApiModel> get filteredApiRecruitment {
    if (searchQuery.value.isEmpty) {
      return apiRecruitmentList;
    }

    return apiRecruitmentList.where((recruitment) {
      final name = recruitment.fullName?.toLowerCase() ?? '';
      return name.contains(searchQuery.value.toLowerCase());
    }).toList();
  }

  // Refresh data (untuk pull-to-refresh)
  Future<void> refreshData() async {
    await Future.wait([fetchDraftForms(), fetchApiRecruitmentList()]);
  }

  // Navigasi ke halaman form untuk melanjutkan pengisian
  void continueForm(String formId) async {
    await Get.toNamed(Routes.KEAGENAN_FORM, parameters: {'formId': formId});
    // Always refresh data when returning from form
    await refreshData();
  }

  // Navigasi ke halaman form baru
  void createNewForm() async {
    await Get.toNamed(Routes.KEAGENAN_FORM);
    // Always refresh data when returning from form
    await refreshData();
  }
}
