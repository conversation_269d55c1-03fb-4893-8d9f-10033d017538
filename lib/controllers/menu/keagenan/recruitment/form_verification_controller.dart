import 'dart:developer';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/recruitment_form_controller.dart';
import 'package:pdl_superapp/models/recruitment_form_model.dart';
import 'package:pdl_superapp/models/user_models.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:pdl_superapp/utils/form_validation.dart';
import 'package:shared_preferences/shared_preferences.dart';

class FormVerificationController extends BaseControllers {
  final RecruitmentFormController baseController;

  FormVerificationController({required this.baseController});

  List<String> roleCandidate = ['BP', 'BM', 'BD'];

  // Form Field data - Images
  Rx<File?> ktpImage = Rx<File?>(null);
  Rx<File?> selfieKtpImage = Rx<File?>(null);
  Rx<File?> pasFotoImage = Rx<File?>(null);

  // Image URLs from upload
  RxString ktpUrl = ''.obs;
  RxString selfieKtpUrl = ''.obs;
  RxString pasFotoUrl = ''.obs;

  // Upload progress indicators
  RxDouble ktpUploadProgress = 0.0.obs;
  RxDouble selfieKtpUploadProgress = 0.0.obs;
  RxDouble pasFotoUploadProgress = 0.0.obs;

  // Upload status
  RxBool isKtpUploading = false.obs;
  RxBool isSelfieKtpUploading = false.obs;
  RxBool isPasFotoUploading = false.obs;

  // Form Verification - menggunakan RxString untuk recruiter info
  final recruiterName = RxString('');
  final recruiterId = RxString('');
  final recruiterBranch = RxString('');
  final recruiterCode = RxString('');
  final recruiterLevel = RxString('');
  final recruiterPhoto = RxString('');
  final candidateLevelController = TextEditingController();
  final candidateBranchController = TextEditingController();
  final candidateBranchCode = RxInt(0);

  // BranchList
  RxList<BranchModels> branchList = RxList();
  RxBool isAgentLoading = false.obs;

  // Validation errors
  RxString ktpImageError = ''.obs;
  RxString selfieKtpImageError = ''.obs;
  RxString pasFotoImageError = ''.obs;
  RxString candidateLevelError = ''.obs;
  RxString candidateBranchError = ''.obs;

  @override
  void onInit() async {
    super.onInit();

    // setup roles candidate yang bisa di pilih
    // BP => BP
    // BM => BP, BM
    // BD up => BP, BM, BD
    String recruiterLevel =
        baseController.prefs.getString(kStorageUserLevel) ?? '';
    switch (recruiterLevel) {
      case kLevelBP:
        roleCandidate.retainWhere((item) => item == 'BP');
        break;
      case kLevelBM:
        roleCandidate.retainWhere((item) => item == 'BP' || item == 'BM');
        break;
      default:
    }

    api.getComboCategoryById(
      controllers: this,
      key: 'AgentLevel',
      code: kReqGetComboBoxLevel,
    );
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    setLoading(false);
    switch (requestCode) {
      case kReqGetBranch:
        parseDataBranch(response);
        break;
      default:
    }
  }

  void parseDataBranch(response) {
    branchList.clear();
    branchList.assignAll(
      (response['content'] as List)
          .map((item) => BranchModels.fromJson(item))
          .toList(),
    );
    isAgentLoading.value = false;
    log('Branch list loaded: ${branchList.length} items');
  }

  // Muat data recruiter dari SharedPreferences
  void loadRecruiterDataFromPrefs() {
    if (recruiterName.value.isEmpty) {
      recruiterName.value =
          baseController.prefs.getString(kStorageAgentName) ?? '';
      recruiterId.value = baseController.prefs.getString(kStorageUserId) ?? '';
      recruiterCode.value =
          baseController.prefs.getString(kStorageAgentCode) ?? '';
      recruiterBranch.value =
          baseController.prefs.getString(kStorageAgentBranch) ?? '';
      recruiterLevel.value =
          baseController.prefs.getString(kStorageUserLevel) ?? '';
    }
  }

  // Branch onTextUpdate
  void onBranchTextChanged(String value) {
    String params = "branchName=$value";
    if (value.length > 2) {
      api.getBranch(controllers: this, code: kReqGetBranch, params: params);
    }
    if (value.length < 2) {
      branchList.clear();
    }
  }

  Future<void> pickKtpImage(String title, String type) async {
    final result = await Get.toNamed(
      Routes.PHOTO_PAGE_PANDUAN,
      arguments: {'title': title, 'type': type},
    );
    if (result != null && result is File) {
      try {
        // Rename file
        File renamedFile = await changeFileNameOnly(
          result,
          'foto-${title.replaceAll(' ', '-').toLowerCase()}-${Utils.getRandomString(length: 6)}.jpg',
        );

        // Simpan file yang sudah di-rename
        if (type == kPhotoTypeKtp) {
          ktpImage.value = renamedFile;
        } else if (type == kPhotoTypeSelfieKtp) {
          selfieKtpImage.value = renamedFile;
        } else if (type == kPhotoTypePasFoto) {
          pasFotoImage.value = renamedFile;
        }

        log("File berhasil di-rename: ${renamedFile.path}");

        baseController.processKtpOcr(renamedFile);

        // Upload image setelah berhasil di-rename
        await uploadImage(renamedFile, type);

        // Notify parent controller about form change
        try {
          baseController.onFormChanged();
        } catch (e) {
          log('Parent controller not found: $e');
        }
      } catch (e) {
        log("Error saat memproses file KTP: $e");
        // Jika rename gagal, tetap gunakan file asli
        if (title == 'KTP') {
          ktpImage.value = result;
        } else if (title == 'Selfie Dengan KTP') {
          selfieKtpImage.value = result;
        } else if (title == 'Pas Foto') {
          pasFotoImage.value = result;
        }

        // Upload image meskipun rename gagal
        await uploadImage(result, type);

        // Notify parent controller about form change
        try {
          baseController.onFormChanged();
        } catch (e) {
          log('Parent controller not found: $e');
        }
      }
    }
  }

  Future<File> changeFileNameOnly(File file, String newFileName) async {
    try {
      if (!await file.exists()) {
        throw Exception("File tidak ditemukan: ${file.path}");
      }

      var path = file.path;
      var lastSeparator = path.lastIndexOf(Platform.pathSeparator);
      var newPath = path.substring(0, lastSeparator + 1) + newFileName;

      // Gunakan copy + delete untuk menghindari race condition
      // Copy file ke lokasi baru
      File oldFile = File(newPath);
      if (await oldFile.exists()) {
        await oldFile.delete();
      }

      File copiedFile = await file.copy(newPath);

      // Hapus file asli setelah copy berhasil
      await file.delete();

      return copiedFile;
    } catch (e) {
      log("Error saat rename file: $e");
      // Kembalikan file asli jika terjadi error
      return file;
    }
  }

  // Upload image to server using API class
  Future<void> uploadImage(File imageFile, String type) async {
    try {
      // Set upload status
      switch (type) {
        case kPhotoTypeKtp:
          isKtpUploading.value = true;
          ktpUploadProgress.value = 0.0;
          break;
        case kPhotoTypeSelfieKtp:
          isSelfieKtpUploading.value = true;
          selfieKtpUploadProgress.value = 0.0;
          break;
        case kPhotoTypePasFoto:
          isPasFotoUploading.value = true;
          pasFotoUploadProgress.value = 0.0;
          break;
      }

      // Call API upload function
      final result = await api.uploadRecruitmentImage(
        imageFile: imageFile,
        type: type,
        onProgress: (percent) {
          // Update progress
          switch (type) {
            case kPhotoTypeKtp:
              ktpUploadProgress.value = percent;
              break;
            case kPhotoTypeSelfieKtp:
              selfieKtpUploadProgress.value = percent;
              break;
            case kPhotoTypePasFoto:
              pasFotoUploadProgress.value = percent;
              break;
          }
        },
      );
      if (result != null && result['success'] == true) {
        final uploadedUrl = result['url'] as String?;
        if (uploadedUrl != null && uploadedUrl.isNotEmpty) {
          // Store the URL
          switch (type) {
            case kPhotoTypeKtp:
              ktpUrl.value = uploadedUrl;
              break;
            case kPhotoTypeSelfieKtp:
              selfieKtpUrl.value = uploadedUrl;
              break;
            case kPhotoTypePasFoto:
              pasFotoUrl.value = uploadedUrl;
              break;
          }
          log("Upload berhasil untuk $type: $uploadedUrl");
        }
      } else {
        final message = result?['message'] ?? 'Upload gagal';
        log("Upload gagal untuk $type: $message");
      }
    } catch (e) {
      log("Error saat upload gambar $type: $e");
    } finally {
      // Reset upload status
      switch (type) {
        case kPhotoTypeKtp:
          isKtpUploading.value = false;
          ktpUploadProgress.value = 0.0;
          break;
        case kPhotoTypeSelfieKtp:
          isSelfieKtpUploading.value = false;
          selfieKtpUploadProgress.value = 0.0;
          break;
        case kPhotoTypePasFoto:
          isPasFotoUploading.value = false;
          pasFotoUploadProgress.value = 0.0;
          break;
      }
    }
  }

  // Clear image method
  void clearImage(String type) {
    switch (type) {
      case kPhotoTypeKtp:
        ktpImage.value?.delete();
        ktpImage.value = null;
        ktpUrl.value = '';
        break;
      case kPhotoTypeSelfieKtp:
        selfieKtpImage.value?.delete();
        selfieKtpImage.value = null;
        selfieKtpUrl.value = '';
        break;
      case kPhotoTypePasFoto:
        pasFotoImage.value?.delete();
        pasFotoImage.value = null;
        pasFotoUrl.value = '';
        break;
    }
    // Notify parent controller about form change
    try {
      baseController.onFormChanged();
    } catch (e) {
      log('Parent controller not found: $e');
    }
  }

  // Populate form data from model
  void populateFormData(RecruitmentFormModel formData) {
    // Isi Form Verification dengan data - RxString
    recruiterName.value = formData.recruiterName ?? '';
    recruiterId.value = formData.recruiterId ?? '';
    recruiterBranch.value = formData.recruiterBranch ?? '';
    recruiterCode.value = formData.recruiterCode ?? '';
    recruiterLevel.value = formData.recruiterLevel ?? '';
    recruiterPhoto.value = formData.recruiterPhoto ?? '';
    candidateLevelController.text =
        formData.candidateLevel == '' ? 'BP' : formData.candidateLevel ?? 'BP';
    candidateBranchController.text = formData.candidateBranch ?? '';
    candidateBranchCode.value = formData.candidateBranchCode ?? 0;

    // Load image URLs
    ktpUrl.value = formData.ktpImageUrl ?? '';
    selfieKtpUrl.value = formData.selfieKtpImageUrl ?? '';
    pasFotoUrl.value = formData.pasFotoImageUrl ?? '';
  }

  // Validate form verification
  bool validateForm() {
    bool isValid = true;

    // Clear previous errors
    ktpImageError.value = '';
    selfieKtpImageError.value = '';
    pasFotoImageError.value = '';
    candidateLevelError.value = '';
    candidateBranchError.value = '';

    // Validate KTP image
    if (ktpImage.value == null && ktpUrl.value.isEmpty) {
      ktpImageError.value = 'Foto KTP tidak boleh kosong';
      isValid = false;
    }

    // Validate Selfie KTP image
    if (selfieKtpImage.value == null && selfieKtpUrl.value.isEmpty) {
      selfieKtpImageError.value = 'Selfie dengan KTP tidak boleh kosong';
      isValid = false;
    }

    // Validate Pas Foto image
    if (pasFotoImage.value == null && pasFotoUrl.value.isEmpty) {
      pasFotoImageError.value = 'Pas foto tidak boleh kosong';
      isValid = false;
    }
    // Validate candidate level
    final levelError = FormValidation.validateRequired(
      candidateLevelController.text,
      'Level keagenan kandidat',
    );
    if (levelError != null) {
      candidateLevelError.value = levelError;
      isValid = false;
    }

    // Validate candidate branch
    final branchError = FormValidation.validateRequired(
      candidateBranchController.text,
      'Kantor cabang',
    );
    if (branchError != null) {
      candidateBranchError.value = branchError;
      isValid = false;
    }

    return isValid;
  }

  @override
  void onClose() {
    candidateLevelController.dispose();
    candidateBranchController.dispose();
    candidateBranchCode.value = 0;
    super.onClose();
  }
}
