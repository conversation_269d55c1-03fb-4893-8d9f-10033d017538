import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/components/title_widget.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/recruitment_form_controller.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/form_terms_controller.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:signature/signature.dart';
import 'dart:convert';
import 'dart:typed_data';

class FormTermsSignature extends StatelessWidget {
  final RecruitmentFormController controller;

  const FormTermsSignature({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    final termsController = controller.termsController;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TitleWidget(title: '<PERSON><PERSON><PERSON>'),

        // PKAJ Document
        _documentCard(
          context,
          title: '<PERSON><PERSON><PERSON><PERSON> (PKAJ)',
          onTap: () {
            // Navigate to PKAJ document viewer
            termsController.markPkajDocumentViewed();
          },
          isViewed: termsController.isPkajDocumentViewed,
        ),

        // PMKAJ Document
        _documentCard(
          context,
          title: 'Perjanjian Manajemen Keagenan Asuransi Jiwa (PMKAJ)',
          onTap: () {
            // Navigate to PMKAJ document viewer
            termsController.markPmkajDocumentViewed();
          },
          isViewed: termsController.isPmkajDocumentViewed,
        ),

        // Kode Etik Document
        _documentCard(
          context,
          title: 'Kode Etik Agen Asuransi',
          onTap: () {
            // Navigate to Kode Etik document viewer
            termsController.markKodeEtikDocumentViewed();
          },
          isViewed: termsController.isKodeEtikDocumentViewed,
        ),

        // Anti Twisting Document
        _documentCard(
          context,
          title: 'Peraturan Anti Twisting',
          onTap: () {
            // Navigate to Anti Twisting document viewer
            termsController.markAntiTwistingDocumentViewed();
          },
          isViewed: termsController.isAntiTwistingDocumentViewed,
        ),

        _padding(
          Text(
            'Paraf dan Tanda Tangan',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w700),
          ),
        ),
        // Warning when not all TNC are checked
        Obx(
          () =>
              termsController.isAllTermsRead.isTrue
                  ? Container()
                  : _padding(
                    Container(
                      width: Get.width,
                      padding: EdgeInsets.all(paddingMedium),
                      decoration: BoxDecoration(
                        color: kColorGlobalBgRed,
                        borderRadius: BorderRadius.circular(radiusSmall),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.warning_amber_rounded,
                            color: kColorGlobalRed,
                          ),
                          SizedBox(width: paddingSmall),
                          Expanded(
                            child: Text(
                              'Mohon membaca seluruh dokumen diatas sebelum menandatangani dokumen',
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(color: kColorGlobalRed),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
        ),
        // Paraf & ttd muncul ketika all the TNC are checked
        _padding(
          Obx(
            () =>
                termsController.isAllTermsRead.value
                    ? SizedBox(
                      width: Get.width,
                      child: Row(
                        children: [
                          _parafCard(context, termsController),
                          SizedBox(width: paddingMedium),
                          _signatureCard(context, termsController),
                        ],
                      ),
                    )
                    : SizedBox.shrink(),
          ),
        ),
        _padding(
          Obx(
            () => GestureDetector(
              onTap:
                  () => termsController.toggleAgreement(
                    !termsController.isAgreementChecked.value,
                  ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    termsController.isAgreementChecked.value
                        ? Icons.check_box_rounded
                        : Icons.check_box_outline_blank_rounded,
                    color:
                        termsController.isAgreementChecked.value
                            ? kColorGlobalBlue
                            : (Get.isDarkMode
                                ? kColorBorderDark
                                : kColorBorderLight),
                  ),
                  SizedBox(width: paddingSmall),
                  Expanded(
                    child: Text(
                      'Dengan menandatangani dokumen berikut, saya (kandidat) telah membaca dan menyetujui seluruh dokumen perjanjian keagenan ini.',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color:
                            Get.isDarkMode
                                ? kColorTextTersier
                                : kColorTextTersierLight,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        _padding(Divider()),
        _padding(
          Text(
            'Anda dapat membagikan halaman ini untuk ditandatangani secara mandiri oleh kandidat.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color:
                  Get.isDarkMode ? kColorTextTersier : kColorTextTersierLight,
            ),
          ),
        ),
        _padding(
          SizedBox(
            width: Get.width,
            child: Obx(
              () => PdlButton(
                title: 'Bagikan',
                onPressed:
                    termsController.isSharing.value
                        ? null
                        : () => termsController.shareForm(),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Padding _documentCard(
    BuildContext context, {
    required String title,
    required Function() onTap,
    required RxBool isViewed,
  }) {
    return _padding(
      Obx(
        () => GestureDetector(
          onTap: onTap,
          child: Container(
            width: Get.width,
            padding: EdgeInsets.all(paddingMedium),
            decoration: BoxDecoration(
              border: Border.all(
                color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
              ),
              borderRadius: BorderRadius.circular(radiusSmall),
            ),
            child: Row(
              children: [
                Icon(
                  isViewed.isTrue
                      ? Icons.check_circle
                      : Icons.check_circle_outline,
                  color:
                      isViewed.isTrue
                          ? kColorGlobalGreen
                          : (Get.isDarkMode
                              ? kColorBorderDark
                              : kColorBorderLight),
                ),
                SizedBox(width: paddingSmall),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ),
                SizedBox(width: paddingSmall),
                Icon(Icons.chevron_right),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Padding _padding(Widget child) {
    return Padding(
      padding: const EdgeInsets.only(top: paddingMedium),
      child: child,
    );
  }

  Expanded _parafCard(
    BuildContext context,
    FormTermsController termsController,
  ) {
    return Expanded(
      child: Obx(
        () => Container(
          padding: EdgeInsets.all(paddingMedium),
          decoration: BoxDecoration(
            color: kColorGlobalBgBlue,
            borderRadius: BorderRadius.circular(radiusMedium),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Wrap(
                crossAxisAlignment: WrapCrossAlignment.center,
                children: [
                  Text(
                    'Paraf',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: kColorPaninBlue,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  if (termsController.isParafUploading.value)
                    Padding(
                      padding: EdgeInsets.only(left: paddingSmall),
                      child: SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: kColorGlobalBlue,
                        ),
                      ),
                    )
                  else if (termsController.isParafCompleted.value)
                    Padding(
                      padding: EdgeInsets.only(left: paddingSmall),
                      child: Icon(
                        Icons.check_circle,
                        color: kColorGlobalGreen,
                        size: 16,
                      ),
                    ),
                ],
              ),
              SizedBox(height: paddingSmall),
              GestureDetector(
                onTap: () {
                  _showSignaturePad(
                    context,
                    title: 'Paraf',
                    onSave: (signatureData) {
                      termsController.setParafData(signatureData);
                    },
                    currentData: termsController.parafData.value,
                  );
                },
                child: Container(
                  width: Get.width,
                  padding: EdgeInsets.all(paddingMedium),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface,
                    borderRadius: BorderRadius.circular(radiusMedium),
                  ),
                  child: AspectRatio(
                    aspectRatio: 1,
                    child:
                        termsController.isParafCompleted.value
                            ? termsController.parafData.value.isNotEmpty
                                ? ClipRRect(
                                  borderRadius: BorderRadius.circular(
                                    radiusMedium,
                                  ),
                                  child: Image.memory(
                                    base64Decode(
                                      termsController.parafData.value,
                                    ),
                                    fit: BoxFit.contain,
                                    errorBuilder: (context, error, stackTrace) {
                                      return Center(
                                        child: Text(
                                          'Paraf\nTersimpan',
                                          textAlign: TextAlign.center,
                                          style: Theme.of(
                                            context,
                                          ).textTheme.bodySmall?.copyWith(
                                            color: kColorGlobalGreen,
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                )
                                : Center(
                                  child: Text(
                                    'Paraf\nTersimpan',
                                    textAlign: TextAlign.center,
                                    style: Theme.of(context).textTheme.bodySmall
                                        ?.copyWith(color: kColorGlobalGreen),
                                  ),
                                )
                            : Center(
                              child: Icon(
                                Icons.edit,
                                color:
                                    Get.isDarkMode
                                        ? kColorTextTersier
                                        : kColorTextTersierLight,
                              ),
                            ),
                  ),
                ),
              ),
              if (termsController.parafError.value.isNotEmpty)
                Padding(
                  padding: EdgeInsets.only(top: paddingSmall),
                  child: Text(
                    termsController.parafError.value,
                    style: Theme.of(
                      context,
                    ).textTheme.bodySmall?.copyWith(color: kColorGlobalRed),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Expanded _signatureCard(
    BuildContext context,
    FormTermsController termsController,
  ) {
    return Expanded(
      child: Obx(
        () => Container(
          padding: EdgeInsets.all(paddingMedium),
          decoration: BoxDecoration(
            color: kColorGlobalBgBlue,
            borderRadius: BorderRadius.circular(radiusMedium),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Wrap(
                crossAxisAlignment: WrapCrossAlignment.center,
                children: [
                  Text(
                    'Tanda Tangan',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: kColorPaninBlue,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  if (termsController.isSignatureUploading.value)
                    Padding(
                      padding: EdgeInsets.only(left: paddingSmall),
                      child: SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: kColorGlobalBlue,
                        ),
                      ),
                    )
                  else if (termsController.isSignatureCompleted.value)
                    Padding(
                      padding: EdgeInsets.only(left: paddingSmall),
                      child: Icon(
                        Icons.check_circle,
                        color: kColorGlobalGreen,
                        size: 16,
                      ),
                    ),
                ],
              ),
              SizedBox(height: paddingSmall),
              GestureDetector(
                onTap: () {
                  _showSignaturePad(
                    context,
                    title: 'Tanda Tangan',
                    onSave: (signatureData) {
                      termsController.setSignatureData(signatureData);
                    },
                    currentData: termsController.signatureData.value,
                  );
                },
                child: Container(
                  width: Get.width,
                  padding: EdgeInsets.all(paddingMedium),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface,
                    borderRadius: BorderRadius.circular(radiusMedium),
                  ),
                  child: AspectRatio(
                    aspectRatio: 1,
                    child:
                        termsController.isSignatureCompleted.value
                            ? termsController.signatureData.value.isNotEmpty
                                ? ClipRRect(
                                  borderRadius: BorderRadius.circular(
                                    radiusMedium,
                                  ),
                                  child: Image.memory(
                                    base64Decode(
                                      termsController.signatureData.value,
                                    ),
                                    fit: BoxFit.contain,
                                    errorBuilder: (context, error, stackTrace) {
                                      return Center(
                                        child: Text(
                                          'Tanda Tangan\nTersimpan',
                                          textAlign: TextAlign.center,
                                          style: Theme.of(
                                            context,
                                          ).textTheme.bodySmall?.copyWith(
                                            color: kColorGlobalGreen,
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                )
                                : Center(
                                  child: Text(
                                    'Tanda Tangan\nTersimpan',
                                    textAlign: TextAlign.center,
                                    style: Theme.of(context).textTheme.bodySmall
                                        ?.copyWith(color: kColorGlobalGreen),
                                  ),
                                )
                            : Center(
                              child: Icon(
                                Icons.edit,
                                color:
                                    Get.isDarkMode
                                        ? kColorTextTersier
                                        : kColorTextTersierLight,
                              ),
                            ),
                  ),
                ),
              ),
              if (termsController.signatureError.value.isNotEmpty)
                Padding(
                  padding: EdgeInsets.only(top: paddingSmall),
                  child: Text(
                    termsController.signatureError.value,
                    style: Theme.of(
                      context,
                    ).textTheme.bodySmall?.copyWith(color: kColorGlobalRed),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  void _showSignaturePad(
    BuildContext context, {
    required String title,
    required Function(String) onSave,
    required String currentData,
  }) {
    final SignatureController signatureController = SignatureController(
      penStrokeWidth: 2,
      penColor: Get.isDarkMode ? Colors.white : Colors.black,
      exportBackgroundColor: Get.isDarkMode ? Colors.black : Colors.white,
    );

    // Note: The signature package doesn't support loading from base64 directly
    // We'll show empty pad but indicate there's existing data in the UI

    Get.dialog(
      Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          margin: EdgeInsets.all(paddingMedium),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(radiusMedium),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(paddingMedium),
                decoration: BoxDecoration(
                  // color: kColorGlobalBgBlue,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(radiusMedium),
                    topRight: Radius.circular(radiusMedium),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        title,
                        style: Theme.of(
                          context,
                        ).textTheme.titleMedium?.copyWith(
                          color: kColorPaninBlue,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                    ),
                    GestureDetector(
                      onTap: () => Get.back(),
                      child: Icon(Icons.close, color: kColorPaninBlue),
                    ),
                  ],
                ),
              ),

              // Signature pad area
              Container(
                margin: EdgeInsets.all(paddingMedium),
                height: 200,
                decoration: BoxDecoration(
                  border: Border.all(
                    color:
                        Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
                  ),
                  borderRadius: BorderRadius.circular(radiusSmall),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(radiusSmall),
                  child: Signature(
                    controller: signatureController,
                    backgroundColor:
                        Get.isDarkMode ? Colors.black : Colors.white,
                  ),
                ),
              ),

              // Current signature indicator
              if (currentData.isNotEmpty)
                Container(
                  margin: EdgeInsets.symmetric(horizontal: paddingMedium),
                  padding: EdgeInsets.all(paddingSmall),
                  decoration: BoxDecoration(
                    color: kColorGlobalBgGreen,
                    borderRadius: BorderRadius.circular(radiusSmall),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.check_circle,
                        color: kColorGlobalGreen,
                        size: 16,
                      ),
                      SizedBox(width: paddingSmall),
                      Expanded(
                        child: Text(
                          'Terdapat $title yang tersimpan. Buat baru untuk mengganti.',
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(color: kColorGlobalGreen),
                        ),
                      ),
                    ],
                  ),
                ),

              // Action buttons
              Container(
                padding: EdgeInsets.all(paddingMedium),
                child: Row(
                  children: [
                    Expanded(
                      child: PdlButton(
                        title: 'Ulangi',
                        onPressed: () {
                          signatureController.clear();
                        },
                        backgroundColor: Colors.transparent,
                        foregorundColor:
                            Get.isDarkMode ? kColorTextDark : kColorTextLight,
                        borderColor:
                            Get.isDarkMode
                                ? kColorBorderDark
                                : kColorBorderLight,
                      ),
                    ),
                    SizedBox(width: paddingMedium),
                    Expanded(
                      child: PdlButton(
                        title: 'Simpan',
                        onPressed: () async {
                          if (signatureController.isNotEmpty) {
                            final Uint8List? signature =
                                await signatureController.toPngBytes();
                            if (signature != null) {
                              final String base64String = base64Encode(
                                signature,
                              );
                              onSave(base64String);
                              Get.back();
                            }
                          } else {
                            // If empty but there was existing data, clear it
                            if (currentData.isNotEmpty) {
                              onSave('');
                              Get.back();
                            } else {
                              Get.snackbar(
                                'Peringatan',
                                'Silakan buat $title terlebih dahulu',
                                backgroundColor: kColorGlobalBgRed,
                                colorText: kColorGlobalRed,
                              );
                            }
                          }
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
