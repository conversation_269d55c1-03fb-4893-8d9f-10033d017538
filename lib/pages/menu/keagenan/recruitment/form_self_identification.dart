import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/pdl_drop_down.dart';
import 'package:pdl_superapp/components/pdl_text_field.dart';
import 'package:pdl_superapp/components/title_widget.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/form_self_identification_controller.dart';
import 'package:pdl_superapp/utils/constants.dart';

class FormSelfIdentification extends StatelessWidget {
  final FormSelfIdentificationController controller;

  const FormSelfIdentification({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TitleWidget(title: 'Kelengkapan Data Pribadi'),
        Text(
          'Mohon mengisi data dengan benar, tanpa kesalahan.',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Get.isDarkMode ? kColorTextTersier : kColorTextTersierLight,
            height: 2,
          ),
        ),
        SizedBox(height: paddingMedium),
        _infoContact(context),
        SizedBox(height: paddingMedium),
        _emergencyContact(context),
        SizedBox(height: paddingMedium),
        _bankSection(context),
        SizedBox(height: paddingMedium),
      ],
    );
  }

  Column _infoContact(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Informasi & Kontak',
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700),
        ),
        _padding(
          Obx(
            () => PdlTextField(
              label: 'Email',
              textController: controller.emailController,
              hasError: controller.emailError.value.isNotEmpty,
              errorText:
                  controller.emailError.value.isEmpty
                      ? null
                      : controller.emailError.value,
            ),
          ),
        ),
        _padding(
          Obx(
            () => PdlTextField(
              label: 'Nomor HP',
              isPhoneNumber: true,
              textController: controller.nomorHpController,
              hasError: controller.nomorHpError.value.isNotEmpty,
              errorText:
                  controller.nomorHpError.value.isEmpty
                      ? null
                      : controller.nomorHpError.value,
            ),
          ),
        ),
        _padding(
          Obx(
            () => PdlDropDown(
              item: [
                for (int i = 0; i < controller.occupationList.length; i++)
                  controller.occupationList[i].value ?? '-',
                'Pilih',
              ],
              selectedItem:
                  controller.pekerjaanController.text.isNotEmpty
                      ? controller.pekerjaanController.text
                      : controller.occupationList.isNotEmpty
                      ? controller.occupationList[0].value ?? 'Pilih'
                      : 'Pilih',
              title: 'Pekerjaan',
              enabled: true,
              onChanged: (val) {
                controller.pekerjaanController.text = val ?? '';
                controller.pekerjaanCodeController.text = val ?? '';
                controller.pekerjaanCodeController.text =
                    controller.occupationList
                        .firstWhere((item) => item.value == val)
                        .key ??
                    '';
              },
              hasError: controller.pekerjaanError.value.isNotEmpty,
              errorText:
                  controller.pekerjaanError.value.isEmpty
                      ? null
                      : controller.pekerjaanError.value,
            ),
          ),
        ),
      ],
    );
  }

  Column _emergencyContact(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Kontak Darurat',
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700),
        ),
        _padding(
          Obx(
            () => PdlTextField(
              label: 'Nama',
              textController: controller.emergencyNamaController,
              hasError: controller.emergencyNamaError.value.isNotEmpty,
              errorText:
                  controller.emergencyNamaError.value.isEmpty
                      ? null
                      : controller.emergencyNamaError.value,
            ),
          ),
        ),
        _padding(
          PdlDropDown(
            item: controller.emergencyContactStatusList,
            selectedItem: controller.emergencyHubunganController.text,
            title: 'Hubungan Dengan Anda',
            enabled: true,
            onChanged: (val) {
              controller.emergencyHubunganController.text = val!;
            },
            disableSearch: true,
          ),
        ),
        _padding(
          Obx(
            () => PdlTextField(
              label: 'Hubungan Dengan Anda',
              textController: controller.emergencyHubunganController,
              hasError: controller.emergencyHubunganError.value.isNotEmpty,
              errorText:
                  controller.emergencyHubunganError.value.isEmpty
                      ? null
                      : controller.emergencyHubunganError.value,
            ),
          ),
        ),
        _padding(
          Obx(
            () => PdlTextField(
              label: 'Nomor HP',
              isPhoneNumber: true,
              textController: controller.emergencyNomorHpController,
              hasError: controller.emergencyNomorHpError.value.isNotEmpty,
              errorText:
                  controller.emergencyNomorHpError.value.isEmpty
                      ? null
                      : controller.emergencyNomorHpError.value,
            ),
          ),
        ),
      ],
    );
  }

  Column _bankSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Nomor Rekening',
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700),
        ),
        _padding(
          Obx(
            () => PdlTextField(
              label: 'Nama Pemilik Rekening',
              textController: controller.namaPemilikRekeningController,
              hasError: controller.namaPemilikRekeningError.value.isNotEmpty,
              errorText:
                  controller.namaPemilikRekeningError.value.isEmpty
                      ? null
                      : controller.namaPemilikRekeningError.value,
            ),
          ),
        ),
        _padding(
          Obx(
            () => PdlTextField(
              label: 'Nomor Rekening',
              textController: controller.nomorRekeningController,
              hasError: controller.nomorRekeningError.value.isNotEmpty,
              errorText:
                  controller.nomorRekeningError.value.isEmpty
                      ? null
                      : controller.nomorRekeningError.value,
            ),
          ),
        ),
        _padding(
          Obx(
            () => PdlTextField(
              textController: controller.namaBankController,
              hint: 'Bank',
              label: 'Bank',
              onChanged: (val) => controller.onBankTextChanged(val),
              prefixIcon: Icon(Icons.search),
              hasError: controller.namaBankError.value.isNotEmpty,
              errorText:
                  controller.namaBankError.value.isEmpty
                      ? null
                      : controller.namaBankError.value,
              items: [
                for (int i = 0; i < controller.bankList.length; i++)
                  GestureDetector(
                    onTap: () {
                      controller.namaBankController.text =
                          controller.bankList[i].bankName ?? '-';
                      controller.bankCode.value =
                          controller.bankList[i].id ?? 0;
                      controller.bankList.clear();
                    },
                    child: Container(
                      width: Get.width,
                      color: Colors.transparent,
                      padding: EdgeInsets.only(top: paddingSmall),
                      child: Text(controller.bankList[i].bankName ?? '-'),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Padding _padding(Widget child) {
    return Padding(
      padding: const EdgeInsets.only(top: paddingMedium),
      child: child,
    );
  }
}
