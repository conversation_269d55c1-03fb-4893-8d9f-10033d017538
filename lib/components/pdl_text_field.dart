import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class PdlTextField extends StatelessWidget {
  final String? label;
  final String? hint;
  final bool? isPassword;
  final Color? fillColor;
  final bool? enabled;
  final bool? isTextArea;
  final String? initialValue;
  final TextEditingController? textController;
  final Function(String)? onChanged;
  final Color? borderColor;
  final Widget? prefixIcon;
  final Widget? suffix;
  final int? maxLength;
  final TextInputAction? textInputAction;
  final TextInputType? keyboardType;
  final double height;
  final Function()? onEditingComplete;
  final bool? isPhoneNumber;
  final List<Widget>? items;
  final String? errorText;
  final bool hasError;
  const PdlTextField({
    super.key,
    this.hint,
    this.label,
    this.isPassword,
    this.enabled,
    this.fillColor,
    this.isTextArea,
    this.initialValue,
    this.textController,
    this.onChanged,
    this.borderColor,
    this.prefixIcon,
    this.onEditingComplete,
    this.suffix,
    this.maxLength,
    this.textInputAction,
    this.keyboardType,
    this.height = 48,
    this.isPhoneNumber,
    this.items,
    this.errorText,
    this.hasError = false,
  });

  @override
  Widget build(BuildContext context) {
    RxBool isObsecured = (isPassword ?? false).obs;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (label != null)
          Text(label!, style: Theme.of(context).textTheme.bodyMedium),
        if (label != null) SizedBox(height: paddingSmall),
        Obx(
          () => SizedBox(
            height: height,
            child: Row(
              children: [
                if (isPhoneNumber == true)
                  Container(
                    height: height,
                    decoration: BoxDecoration(
                      color:
                          Get.isDarkMode
                              ? kColorBorderDark
                              : kColorBorderLight.withValues(alpha: 0.5),
                      border: Border(
                        bottom: getPhoneBorder(),
                        left: getPhoneBorder(),
                        top: getPhoneBorder(),
                      ),
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(radiusSmall),
                        bottomLeft: Radius.circular(radiusSmall),
                      ),
                    ),
                    padding: EdgeInsets.symmetric(horizontal: paddingMedium),
                    child: Center(
                      child: Text(
                        '08',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ),
                  ),
                Expanded(
                  child: TextFormField(
                    style: Theme.of(context).textTheme.bodyMedium,
                    obscureText: isObsecured.value,
                    enabled: enabled,
                    onChanged: onChanged,
                    onEditingComplete: onEditingComplete,
                    maxLines: isTextArea == true ? 3 : 1,
                    textInputAction: textInputAction,
                    keyboardType:
                        isPhoneNumber == true
                            ? TextInputType.phone
                            : keyboardType,
                    controller: textController,
                    initialValue: initialValue,
                    maxLength: maxLength,
                    decoration: InputDecoration(
                      hintText: hint,
                      hintStyle: Theme.of(context).textTheme.bodyMedium
                          ?.copyWith(color: kColorTextTersier),
                      prefixIcon: prefixIcon,
                      suffix: suffix,
                      suffixIcon:
                          isPassword != true
                              ? null
                              : GestureDetector(
                                onTap:
                                    () =>
                                        isObsecured.value = !isObsecured.value,
                                child: Utils.cachedSvgWrapper(
                                  isObsecured.isTrue
                                      ? 'icon/ic-linear-eyeClosed.svg'
                                      : 'icon/ic-linear-eye.svg',
                                  width: 15,
                                  color: Color(0xFFB0B0B0),
                                  fit: BoxFit.none,
                                ),
                              ),

                      fillColor:
                          fillColor ??
                          (enabled == false
                              ? Theme.of(context).colorScheme.secondary
                              : Colors.transparent),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: getBorderRadius(),
                        borderSide: BorderSide(
                          color:
                              hasError
                                  ? Colors.red
                                  : (borderColor ??
                                      Theme.of(context).colorScheme.primary),
                        ),
                      ),
                      disabledBorder: OutlineInputBorder(
                        borderRadius: getBorderRadius(),
                        borderSide: BorderSide(
                          color:
                              hasError
                                  ? Colors.red
                                  : (borderColor ??
                                      (Get.isDarkMode
                                          ? kColorBorderDark
                                          : kColorBorderLight)),
                        ),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: getBorderRadius(),
                        borderSide: BorderSide(
                          color:
                              hasError
                                  ? Colors.red
                                  : (borderColor ??
                                      (Get.isDarkMode
                                          ? kColorBorderDark
                                          : kColorBorderLight)),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        if (errorText != null && errorText!.isNotEmpty)
          Padding(
            padding: EdgeInsets.only(top: paddingSmall),
            child: Text(
              errorText!,
              style: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(color: Colors.red),
            ),
          ),
        if (items != null)
          if (items!.isNotEmpty)
            Container(
              width: Get.width,
              padding: EdgeInsets.all(paddingSmall),
              decoration: BoxDecoration(
                border: Border.all(
                  color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
                ),
                borderRadius: BorderRadius.circular(radiusSmall),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [for (int i = -0; i < items!.length; i++) items![i]],
              ),
            ),
      ],
    );
  }

  BorderRadius getBorderRadius() => BorderRadius.only(
    topLeft: Radius.circular(isPhoneNumber == true ? 0 : radiusSmall),
    topRight: Radius.circular(radiusSmall),
    bottomLeft: Radius.circular(isPhoneNumber == true ? 0 : radiusSmall),
    bottomRight: Radius.circular(radiusSmall),
  );

  BorderSide getPhoneBorder() =>
      BorderSide(color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight);
}
