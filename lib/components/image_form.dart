import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:path/path.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class ImageForm extends StatelessWidget {
  const ImageForm({
    required this.title,
    required this.imageFile,
    required this.onTapAdd,
    this.imageUrl,
    this.description,
    this.uploadText,
    this.isUploading,
    this.uploadProgress,
    this.onClear,
    this.errorText,
    this.hasError = false,
    super.key,
  });

  final String title;
  final Rx<File?> imageFile;
  final Function() onTapAdd;
  final String? description;
  final RxString? imageUrl;
  final String? uploadText;
  final RxBool? isUploading;
  final RxDouble? uploadProgress;
  final Function()? onClear;
  final String? errorText;
  final bool hasError;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: Get.width,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700),
          ),
          SizedBox(height: paddingSmall),
          Obx(() {
            if (imageFile.value != null ||
                (imageUrl?.value.isNotEmpty == true)) {
              // Display the KTP image if available
              return Container(
                padding: EdgeInsets.all(paddingMedium),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(radiusMedium),
                  border: Border.all(
                    color:
                        hasError
                            ? Colors.red
                            : (Get.isDarkMode
                                ? kColorBorderDark
                                : kColorBorderLight),
                  ),
                ),
                width: Get.width,
                child: Row(
                  children: [
                    Container(
                      width: 70,
                      height: 70,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(radiusSmall),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(radiusSmall - 2),
                        child:
                            imageFile.value != null
                                ? Image.file(
                                  imageFile.value!,
                                  fit: BoxFit.cover,
                                )
                                : Utils.cachedImageWrapper(
                                  imageUrl?.value,
                                  isFullUrl: true,
                                  fit: BoxFit.cover,
                                ),
                      ),
                    ),
                    SizedBox(width: paddingSmall),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            basename(imageFile.value?.path ?? 'Image.jpg'),
                            style: Theme.of(context).textTheme.bodyMedium
                                ?.copyWith(fontWeight: FontWeight.w500),
                          ),
                          if (imageFile.value != null)
                            Text(
                              Utils.getFileSize(imageFile.value!.path, 1),
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(fontWeight: FontWeight.w500),
                            ),
                          // Upload progress indicator
                          if (isUploading?.value == true)
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                SizedBox(height: paddingSmall),
                                Text(
                                  'Uploading... ${((uploadProgress?.value ?? 0) * 100).toInt()}%',
                                  style: Theme.of(
                                    context,
                                  ).textTheme.bodySmall?.copyWith(
                                    color: kColorGlobalBlue,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                SizedBox(height: paddingSmall / 2),
                                LinearProgressIndicator(
                                  value: uploadProgress?.value ?? 0,
                                  backgroundColor: Colors.grey[300],
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    kColorGlobalBlue,
                                  ),
                                ),
                              ],
                            ),
                          // GestureDetector(
                          //   onTap:
                          //       () => controller.textCognition(
                          //         File(imageFile.value!.path),
                          //       ),
                          //   child: Text(
                          //     'get data ocr (test only)',
                          //     style: Theme.of(
                          //       context,
                          //     ).textTheme.bodyMedium?.copyWith(
                          //       fontWeight: FontWeight.w700,
                          //       color: kColorGlobalBlue,
                          //       decoration: TextDecoration.underline,
                          //       decorationThickness: 2,
                          //       decorationColor: kColorGlobalBlue,
                          //       decorationStyle: TextDecorationStyle.solid,
                          //     ),
                          //   ),
                          // ),
                        ],
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        if (onClear != null) {
                          onClear!();
                        }
                      },
                      child: Container(
                        color: Colors.transparent,
                        child: Utils.cachedSvgWrapper(
                          'icon/ic-linear-trash-bin.svg',
                          width: 30,
                          color: kColorGlobalRed,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            } else {
              // Show the upload/take photo button if no image
              return GestureDetector(
                onTap: onTapAdd,
                child: Utils.customDottedBorder(
                  child: Container(
                    width: Get.width,
                    color: Colors.transparent,
                    padding: EdgeInsets.all(paddingMedium),
                    child: Column(
                      children: [
                        Utils.cachedSvgWrapper(
                          'icon/ic-linear-image.svg',
                          color: kColorGlobalBlue,
                          width: 40,
                        ),
                        Text(
                          uploadText ?? 'Unggah atau ambil foto',
                          style: Theme.of(
                            context,
                          ).textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w700,
                            color: kColorGlobalBlue,
                            decoration: TextDecoration.underline,
                            decorationThickness: 2,
                            decorationColor: kColorGlobalBlue,
                            decorationStyle: TextDecorationStyle.solid,
                            height: 2,
                          ),
                        ),
                        if (description != null)
                          Text(
                            description!,
                            style: Theme.of(
                              context,
                            ).textTheme.bodyMedium?.copyWith(
                              height: 2,
                              color:
                                  Get.isDarkMode
                                      ? kColorTextTersierLight
                                      : kColorTextTersier,
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              );
            }
          }),
          SizedBox(height: paddingSmall),
          if (errorText != null && errorText!.isNotEmpty)
            Padding(
              padding: EdgeInsets.only(bottom: paddingSmall),
              child: Text(
                errorText!,
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: Colors.red),
              ),
            ),
          Text(
            'Maks. 2MB dengan format JPEG/PNG',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color:
                  Get.isDarkMode ? kColorTextTersierLight : kColorTextTersier,
            ),
          ),
        ],
      ),
    );
  }
}
