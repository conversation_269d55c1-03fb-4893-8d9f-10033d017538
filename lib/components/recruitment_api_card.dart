import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/models/recruitment_api_model.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class RecruitmentApiCard extends StatelessWidget {
  final RecruitmentApiModel recruitment;
  final Function()? onTap;

  const RecruitmentApiCard({super.key, required this.recruitment, this.onTap});

  @override
  Widget build(BuildContext context) {
    // Ambil nama dari recruitment atau tampilkan placeholder jika kosong
    final String name =
        recruitment.fullName?.isNotEmpty == true
            ? recruitment.fullName!
            : 'Kandidat';

    // Tentukan warna status berdasarkan trxStatus
    Color statusColor = kColorGlobalBlue;
    String statusText = recruitment.trxStatus ?? 'UNKNOWN';

    switch (recruitment.trxStatus?.toUpperCase()) {
      case 'DRAFT':
        statusColor = kColorGlobalBlue;
        statusText = 'Draft';
        break;
      case 'IN_PROGRESS':
        statusColor = kColorGlobalWarning;
        statusText = 'In Progress';
        break;
      case 'APPROVED':
        statusColor = kColorGlobalGreen;
        statusText = 'Approved';
        break;
      case 'REJECTED':
        statusColor = kColorGlobalRed;
        statusText = 'Rejected';
        break;
      default:
        statusColor = kColorGlobalBlue;
        statusText = recruitment.trxStatus ?? 'Unknown';
    }

    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: Get.width,
        color: Colors.transparent,
        padding: EdgeInsets.only(
          left: paddingMedium,
          right: paddingMedium,
          top: paddingMedium,
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Avatar dengan inisial nama
                CircleAvatar(
                  backgroundColor: kColorGlobalBlue,
                  child: Text(
                    Utils.getInitials(name),
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                SizedBox(width: paddingSmall),
                Expanded(
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              name,
                              style: Theme.of(context).textTheme.bodyLarge
                                  ?.copyWith(fontWeight: FontWeight.w700),
                            ),
                            Text(
                              recruitment.branch?.branchName ?? '-',
                              style: Theme.of(
                                context,
                              ).textTheme.bodyMedium?.copyWith(
                                color:
                                    Get.isDarkMode
                                        ? kColorTextTersier
                                        : kColorTextTersierLight,
                              ),
                            ),
                            SizedBox(height: paddingSmall),
                            // Tampilkan NIK jika ada
                            if (recruitment.nik?.isNotEmpty == true)
                              Text(
                                'NIK: ${recruitment.nik}',
                                style: Theme.of(
                                  context,
                                ).textTheme.bodySmall?.copyWith(
                                  color:
                                      Get.isDarkMode
                                          ? kColorTextTersier
                                          : kColorTextTersierLight,
                                ),
                              ),
                            SizedBox(height: paddingMedium),
                            Row(
                              children: [
                                Container(
                                  decoration: BoxDecoration(
                                    color: statusColor.withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  padding: EdgeInsets.all(paddingExtraSmall),
                                  child: Text(
                                    statusText,
                                    style: Theme.of(context).textTheme.bodySmall
                                        ?.copyWith(color: statusColor),
                                  ),
                                ),
                                SizedBox(width: paddingSmall),
                                // Tampilkan level posisi jika ada
                                if (recruitment.positionLevel?.isNotEmpty ==
                                    true)
                                  Container(
                                    decoration: BoxDecoration(
                                      color: kColorBorderLight,
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    padding: EdgeInsets.all(paddingExtraSmall),
                                    child: Text(
                                      recruitment.positionLevel!,
                                      style: Theme.of(
                                        context,
                                      ).textTheme.bodySmall?.copyWith(
                                        color:
                                            Get.isDarkMode
                                                ? kColorTextTersier
                                                : kColorTextTersierLight,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      CircleAvatar(
                        backgroundColor: kColorGlobalBgBlue,
                        child: Icon(
                          Icons.chevron_right_outlined,
                          color: kColorGlobalBlue,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: paddingSmall),
            Divider(),
          ],
        ),
      ),
    );
  }
}
